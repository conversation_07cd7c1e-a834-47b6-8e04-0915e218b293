//
//  ItemCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/23.
//

import Foundation
import SwiftUI

// 卡片类型枚举
enum CardType: String, Codable, CaseIterable {
    case scenery = "scenery"    // 风景卡片
    case shopping = "shopping"  // 购物卡片

    // 显示名称
    var displayName: String {
        switch self {
        case .scenery:
            return "风景"
        case .shopping:
            return "购物"
        }
    }
}

// 卡片模型 - 创建后的唯一引用，不包含用户特定信息
struct ItemCard: Codable, Identifiable, Equatable {
    let id: String // 改为String以匹配后端
    let cardType: CardType // 卡片类型：scenery 或 shopping
    let themeColor: String? // 主题色（仅购物卡片具有）
    let coinReward: Int // 碳币奖励
    let experienceReward: Int // 经验奖励
    let description: String
    let title: String
    let imageFileName: String  // 本地存储的文件名
    let imageURL: String // COS返回的URL，供共享使用
    let createdAt: Date // 创建时间戳
    let authorId: String // 存储创建这个卡片的用户id
    /// 位置信息
    let location: String // 位置字符串，用于显示（如"北京市朝阳区"）
    let latitude: Double? // 纬度
    let longitude: Double? // 经度

    // 从 Data 加载图像
    var image: UIImage? {
        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first!.appendingPathComponent(imageFileName)
        if let data = try? Data(contentsOf: url) {
            return UIImage(data: data)
        }
        return nil
    }

    // 格式化创建时间显示
    var formattedCreatedAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: createdAt)
    }

    // 主题色转换为SwiftUI Color
    var themeSwiftUIColor: Color? {
        guard let themeColor = themeColor else { return nil }
        return Color(hex: themeColor)
    }
}
