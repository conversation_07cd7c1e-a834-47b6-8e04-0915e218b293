//
//  User.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation

/// 用户模型 - 匹配后端Prisma模型
struct User: Codable, Identifiable {
    let id: String
    let userId: String
    var nickname: String
    var avatar: String?
    var avatarURL: String?
    var lastActiveTime: Date?
    var createdAt: Date?
    var sharingLocation: Bool
    var carbonCoins: Int

    init(id: String = UUID().uuidString,
         userId: String,
         nickname: String,
         avatar: String? = nil,
         avatarURL: String? = nil,
         lastActiveTime: Date? = nil,
         createdAt: Date? = Date(),
         sharingLocation: Bool = true,
         carbonCoins: Int = 0) {
        self.id = id
        self.userId = userId
        self.nickname = nickname
        self.avatar = avatar
        self.avatarURL = avatarURL
        self.lastActiveTime = lastActiveTime
        self.createdAt = createdAt
        self.sharingLocation = sharingLocation
        self.carbonCoins = carbonCoins
    }
}

// MARK: - API请求和响应模型

/// 用户信息更新请求模型
struct UserUpdateRequest: Codable {
    let userId: String
    let avatarURL: String?
    let nickname: String?
    let lastActiveTime: String?
    let sharingLocation: Bool?
    let carbonCoins: Int?

    init(userId: String,
         avatarURL: String? = nil,
         nickname: String? = nil,
         lastActiveTime: Date? = nil,
         sharingLocation: Bool? = nil,
         carbonCoins: Int? = nil) {
        self.userId = userId
        self.avatarURL = avatarURL
        self.nickname = nickname
        self.lastActiveTime = lastActiveTime?.ISO8601Format()
        self.sharingLocation = sharingLocation
        self.carbonCoins = carbonCoins
    }
}

/// 用户信息获取响应模型
struct UserInfoResponse: Codable {
    let success: Bool
    let data: UserData?
    let error: String?
}

/// 用户信息更新响应模型
struct UserUpdateResponse: Codable {
    let success: Bool
    let data: UserData?
    let error: String?
}

/// 用户数据模型（API响应中的data字段）
struct UserData: Codable {
    let userId: String
    let nickname: String
    let avatar: String?
    let avatarURL: String?
    let lastActiveTime: Date?
    let sharingLocation: Bool?
    let carbonCoins: Int?

    enum CodingKeys: String, CodingKey {
        case userId, nickname, avatar, avatarURL, lastActiveTime, sharingLocation, carbonCoins
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        userId = try container.decode(String.self, forKey: .userId)
        nickname = try container.decode(String.self, forKey: .nickname)
        avatar = try container.decodeIfPresent(String.self, forKey: .avatar)
        avatarURL = try container.decodeIfPresent(String.self, forKey: .avatarURL)
        sharingLocation = try container.decodeIfPresent(Bool.self, forKey: .sharingLocation)
        carbonCoins = try container.decodeIfPresent(Int.self, forKey: .carbonCoins)

        // 处理日期字符串
        if let lastActiveTimeString = try container.decodeIfPresent(String.self, forKey: .lastActiveTime) {
            let formatter = ISO8601DateFormatter()
            lastActiveTime = formatter.date(from: lastActiveTimeString)
        } else {
            lastActiveTime = nil
        }
    }
}
