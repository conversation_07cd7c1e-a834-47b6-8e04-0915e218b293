//
//  Sports.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import Foundation

// MARK: - 时间段枚举
/// 数据查看时间段
enum TimePeriod: String, CaseIterable, Identifiable, Codable {
    // TODO: 此处的id必须为英文？可能考虑根据self返回合适的英文，但是self本身是中文来显示？但是我尝试失败了
    case day = "Day"
    case week = "Week"
    case month = "Month"
    case sixMonths = "0.5 Year"

    var id: String { rawValue }

    /// 显示名称
    var displayName: String {
        return rawValue
    }

    /// 获取对应的天数
    var days: Int {
        switch self {
        case .day:
            return 1
        case .week:
            return 7
        case .month:
            return 30
        case .sixMonths:
            return 180
        }
    }

    /// 获取数据点数量（用于图表显示）
    var dataPoints: Int {
        switch self {
        case .day:
            return 24 // 24小时
        case .week:
            return 7  // 7天
        case .month:
            return 30 // 30天
        case .sixMonths:
            return 30 // 30个数据点（每6天一个点）
        }
    }
}

// MARK: - 步数数据模型
/// 单个步数数据点
struct StepData: Identifiable, Codable, Equatable {
    var id = UUID()
    let date: Date
    let steps: Int
    let calories: Double? // 可选的卡路里数据

    init(date: Date, steps: Int, calories: Double? = nil) {
        self.date = date
        self.steps = steps
        self.calories = calories
    }
}

// MARK: - 运动数据统计
/// 运动数据统计信息
struct SportsStatistics: Codable {
    let totalSteps: Int
    let averageSteps: Double
    let maxSteps: Int
    let minSteps: Int
    let totalCalories: Double?
    let period: TimePeriod
    let changePercentage: Double? // 相比上一周期的变化百分比

    init(stepData: [StepData], period: TimePeriod, previousPeriodAverage: Double? = nil) {
        self.period = period
        self.totalSteps = stepData.reduce(0) { $0 + $1.steps }
        self.averageSteps = stepData.isEmpty ? 0 : Double(totalSteps) / Double(stepData.count)
        self.maxSteps = stepData.map(\.steps).max() ?? 0
        self.minSteps = stepData.map(\.steps).min() ?? 0
        self.totalCalories = stepData.compactMap(\.calories).reduce(0, +)

        // 计算变化百分比
        if let previousAverage = previousPeriodAverage, previousAverage > 0 {
            self.changePercentage = ((averageSteps - previousAverage) / previousAverage) * 100
        } else {
            self.changePercentage = nil
        }
    }
}
