//
//  ScanResult.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import Foundation
import CloudKit

/// 扫描结果模型
struct ScanResult: Codable, Identifiable {
    let id: UUID
    let userId: UUID
    let barcode: String
    let productName: String?
    let carbonImpact: Double // 碳影响值（克）
    let rewardCoins: Int // 奖励碳币
    let environmentalTip: String? // 环保小贴士
    let scannedAt: Date
    
    init(id: UUID = UUID(),
         userId: UUID,
         barcode: String,
         productName: String? = nil,
         carbonImpact: Double = 0,
         rewardCoins: Int = 5,
         environmentalTip: String? = nil) {
        self.id = id
        self.userId = userId
        self.barcode = barcode
        self.productName = productName
        self.carbonImpact = carbonImpact
        self.rewardCoins = rewardCoins
        self.environmentalTip = environmentalTip
        self.scannedAt = Date()
    }
    
    /// 碳影响等级
    var impactLevel: CarbonImpactLevel {
        switch carbonImpact {
        case 0..<50:
            return .low
        case 50..<150:
            return .medium
        case 150...:
            return .high
        default:
            return .unknown
        }
    }
}

/// 碳影响等级
enum CarbonImpactLevel: String, CaseIterable, Codable {
    case low = "low"
    case medium = "medium"
    case high = "high"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .low:
            return "低碳"
        case .medium:
            return "中碳"
        case .high:
            return "高碳"
        case .unknown:
            return "未知"
        }
    }
    
    var color: String {
        switch self {
        case .low:
            return "green"
        case .medium:
            return "orange"
        case .high:
            return "red"
        case .unknown:
            return "gray"
        }
    }
}

// MARK: - CloudKit Support
extension ScanResult {
    /// CloudKit记录类型名称
    static let recordType = "ScanResult"
    
    /// 从CloudKit记录创建扫描结果
    init?(from record: CKRecord) {
        guard let userIdString = record["userId"] as? String,
              let userId = UUID(uuidString: userIdString),
              let barcode = record["barcode"] as? String,
              let carbonImpact = record["carbonImpact"] as? Double,
              let rewardCoins = record["rewardCoins"] as? Int,
              let scannedAt = record["scannedAt"] as? Date else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.userId = userId
        self.barcode = barcode
        self.productName = record["productName"] as? String
        self.carbonImpact = carbonImpact
        self.rewardCoins = rewardCoins
        self.environmentalTip = record["environmentalTip"] as? String
        self.scannedAt = scannedAt
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: ScanResult.recordType, recordID: recordID)
        
        record["userId"] = userId.uuidString
        record["barcode"] = barcode
        record["productName"] = productName
        record["carbonImpact"] = carbonImpact
        record["rewardCoins"] = rewardCoins
        record["environmentalTip"] = environmentalTip
        record["scannedAt"] = scannedAt
        
        return record
    }
}

/// 商品信息模型（用于扫描查询）
struct ProductInfo: Codable, Identifiable {
    let id: UUID
    let barcode: String
    let name: String
    let carbonImpact: Double
    let category: String
    let environmentalTip: String?
    let createdAt: Date
    
    init(id: UUID = UUID(),
         barcode: String,
         name: String,
         carbonImpact: Double,
         category: String = "未分类",
         environmentalTip: String? = nil) {
        self.id = id
        self.barcode = barcode
        self.name = name
        self.carbonImpact = carbonImpact
        self.category = category
        self.environmentalTip = environmentalTip
        self.createdAt = Date()
    }
}

// MARK: - CloudKit Support for ProductInfo
extension ProductInfo {
    /// CloudKit记录类型名称
    static let recordType = "ProductInfo"
    
    /// 从CloudKit记录创建商品信息
    init?(from record: CKRecord) {
        guard let barcode = record["barcode"] as? String,
              let name = record["name"] as? String,
              let carbonImpact = record["carbonImpact"] as? Double,
              let category = record["category"] as? String,
              let createdAt = record["createdAt"] as? Date else {
            return nil
        }
        
        self.id = UUID(uuidString: record.recordID.recordName) ?? UUID()
        self.barcode = barcode
        self.name = name
        self.carbonImpact = carbonImpact
        self.category = category
        self.environmentalTip = record["environmentalTip"] as? String
        self.createdAt = createdAt
    }
    
    /// 转换为CloudKit记录
    func toRecord() -> CKRecord {
        let recordID = CKRecord.ID(recordName: id.uuidString)
        let record = CKRecord(recordType: ProductInfo.recordType, recordID: recordID)
        
        record["barcode"] = barcode
        record["name"] = name
        record["carbonImpact"] = carbonImpact
        record["category"] = category
        record["environmentalTip"] = environmentalTip
        record["createdAt"] = createdAt
        
        return record
    }
}
