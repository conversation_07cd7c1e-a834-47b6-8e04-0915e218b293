//
//  UserItemCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation

// 用户持有的卡片模型 - 记录用户与卡片的关系
struct UserItemCard: Codable, Identifiable, Equatable {
    let id: String // 卡片获得记录的唯一ID (acquisitionRecordId)
    let userId: String // 用户ID
    let cardId: String // 卡片ID
    let acquiredAt: Date // 获取时间（创建或接受传输时）
    let isAuthor: Bool // 是否是作者（true 为作者，便于区分编辑权限）

    // 关联的卡片信息（从API返回时包含）
    let card: ItemCard?

    // 关联的作者信息（从API返回时包含）
    let author: AuthorInfo?

    // 初始化方法 - 用于本地创建
    init(id: String, userId: String, cardId: String, acquiredAt: Date = Date(), isAuthor: Bool = false, card: ItemCard? = nil, author: AuthorInfo? = nil) {
        self.id = id
        self.userId = userId
        self.cardId = cardId
        self.acquiredAt = acquiredAt
        self.isAuthor = isAuthor
        self.card = card
        self.author = author
    }

    // 格式化获取时间显示
    var formattedAcquiredAt: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: acquiredAt)
    }
}

// 作者信息模型（简化版，用于作者信息）
struct AuthorInfo: Codable, Equatable {
    let userId: String
    let nickname: String
    let avatarURL: String?
}
