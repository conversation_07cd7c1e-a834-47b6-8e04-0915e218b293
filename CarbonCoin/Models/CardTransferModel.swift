//
//  CardTransferModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import Foundation

// MARK: - 卡片传输状态

/// 卡片传输状态枚举
enum CardTransferStatus: String, Codable, CaseIterable {
    case pending = "pending"     // 待处理
    case accepted = "accepted"   // 已接受
    case rejected = "rejected"   // 已拒绝

    /// 状态的中文描述
    var localizedDescription: String {
        switch self {
        case .pending:
            return "待处理"
        case .accepted:
            return "已接受"
        case .rejected:
            return "已拒绝"
        }
    }
}

// MARK: - 卡片传输记录模型

/// 卡片传输记录
struct CardTransferRecord: Codable, Identifiable, Equatable {
    let id: String // 传输记录ID
    let senderId: String // 发送者用户ID
    let receiverId: String // 接收者用户ID
    let cardId: String // 卡片ID
    let transferTime: Date // 传输创建时间
    let status: CardTransferStatus // 传输状态

    // 关联的发送者信息（从API返回时包含）
    let sender: UserInfo?

    // 关联的接收者信息（从API返回时包含）
    let receiver: UserInfo?

    // 关联的卡片信息（从API返回时包含）
    let card: CardInfo?

    // 初始化方法
    init(id: String, senderId: String, receiverId: String, cardId: String,
         transferTime: Date = Date(), status: CardTransferStatus = .pending,
         sender: UserInfo? = nil, receiver: UserInfo? = nil, card: CardInfo? = nil) {
        self.id = id
        self.senderId = senderId
        self.receiverId = receiverId
        self.cardId = cardId
        self.transferTime = transferTime
        self.status = status
        self.sender = sender
        self.receiver = receiver
        self.card = card
    }

    // 格式化传输时间显示
    var formattedTransferTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: transferTime)
    }
}

// MARK: - 卡片信息模型（简化版，用于传输记录）

/// 卡片信息（简化版，用于传输记录显示）
struct CardInfo: Codable, Equatable {
    let id: String
    let title: String
    let description: String
    let imageURL: String
    let cardType: String // 卡片类型：scenery 或 shopping
    let themeColor: String? // 主题色（仅购物卡片）
    let coinReward: Int // 碳币奖励
    let experienceReward: Int // 经验奖励
    let location: String // 位置信息
    let createdAt: Date // 创建时间
    let authorId: String? // 原始作者ID（可选，用于保持作者信息）
}

// MARK: - API请求和响应模型

/// 创建卡片传输请求模型
struct CreateCardTransferRequest: Codable {
    let senderId: String
    let receiverId: String
    let cardId: String
}

/// 处理传输请求模型
struct ProcessCardTransferRequest: Codable {
    let transferId: String
    let action: String // "accept" 或 "reject"
    let userId: String // 当前用户ID（必须是接收者）
}

/// 卡片传输响应模型
struct CardTransferResponse: Codable {
    let success: Bool
    let message: String?
    let data: CardTransferRecord?
    let error: String?
}

/// 传输记录列表响应模型
struct CardTransferListResponse: Codable {
    let success: Bool
    let data: CardTransferListData?
    let error: String?
}

/// 传输记录列表数据
struct CardTransferListData: Codable {
    let transfers: CardTransfersByStatus
    let total: Int
}

/// 按状态分组的传输记录
struct CardTransfersByStatus: Codable {
    let pending: [CardTransferRecord]
    let accepted: [CardTransferRecord]
    let rejected: [CardTransferRecord]
}

// MARK: - 卡片传输错误类型

/// 卡片传输错误枚举
enum CardTransferError: Error, LocalizedError {
    case invalidURL
    case missingParameters
    case networkError(Error)
    case serverError(String)
    case decodingError(Error)
    case transferNotFound
    case unauthorizedAccess
    case duplicateTransfer
    case cardNotFound
    case userNotFound
    case unknown(String)

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .missingParameters:
            return "缺少必要参数"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .transferNotFound:
            return "传输记录不存在"
        case .unauthorizedAccess:
            return "无权限执行此操作"
        case .duplicateTransfer:
            return "重复的传输请求"
        case .cardNotFound:
            return "卡片不存在"
        case .userNotFound:
            return "用户不存在"
        case .unknown(let message):
            return message
        }
    }
}
