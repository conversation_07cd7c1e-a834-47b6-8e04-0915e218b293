//
//  PropInteraction.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import Foundation

// MARK: - 道具信息模型

/// 本地道具信息模型
struct PropInfo: Codable, Identifiable {
    let propId: Int
    let propTitle: String
    let defaultRemark: String

    var id: Int { propId }
}

struct PropConfig {
    // 计时器轮询时间
    static let pollingTime: Double = 30
}

// MARK: - 用户信息模型（用于道具交互中的发送者和接收者信息）

/// 道具交互中的用户信息
struct PropInteractionUser: Codable, Identifiable {
    let userId: String
    let nickname: String
    let avatarURL: String?

    var id: String { userId }
}

// MARK: - 道具交互核心模型

/// 道具交互模型（基于schema.prisma中的PropInteraction模型）
struct PropInteraction: Codable, Identifiable {
    let id: String
    let senderUserId: String
    let receiverUserId: String
    let propId: Int
    let remark: String?
    let interactionTime: Date
    let receivedTime: Date?
    let isRead: Bool
    let sender: PropInteractionUser
    let receiver: PropInteractionUser?

    // MARK: - 计算属性

    /// 获取道具信息
    var propInfo: PropInfo? {
        PropInteractionManager.shared.getPropInfo(for: propId)
    }

    /// 获取显示用的备注信息（优先使用用户备注，否则使用默认备注）
    var displayRemark: String {
        if let remark = remark, !remark.isEmpty {
            return remark
        }
        return propInfo?.defaultRemark ?? "发送了一个道具"
    }

    /// 获取道具标题
    var propTitle: String {
        propInfo?.propTitle ?? "未知道具"
    }

    /// 是否为未读消息
    var isUnread: Bool {
        !isRead
    }

    /// 格式化的交互时间
    var formattedInteractionTime: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: interactionTime)
    }

    /// 格式化的接收时间
    var formattedReceivedTime: String? {
        guard let receivedTime = receivedTime else { return nil }
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: receivedTime)
    }
}

// MARK: 拓展道具交互模型的默认值
extension PropInteraction {
    static var `default`: PropInteraction{
        PropInteraction(id: "1", senderUserId: "ffy6511", receiverUserId: "demodemo", propId: 1, remark: nil, interactionTime: Date(), receivedTime: nil, isRead: false, sender: PropInteractionUser(userId: "ffy6511", nickname: "ffy", avatarURL: "https://carboncoin-cos-1358266118.cos.ap-shanghai.myqcloud.com/images/1756460346199_q7woknyb40h.png"), receiver: nil)
    }
}

// MARK: - 道具交互管理器

/// 道具交互管理器，负责管理本地道具信息和效果配置
class PropInteractionManager {
    static let shared = PropInteractionManager()

    // MARK: - 私有属性

    private var propInfoCache: [Int: PropInfo] = [:]
    private let jsonDecoder = JSONDecoder()

    // MARK: - 初始化

    private init() {
        loadPropInfos()
    }

    // MARK: - 公共方法

    /// 获取道具信息
    func getPropInfo(for propId: Int) -> PropInfo? {
        return propInfoCache[propId]
    }

    /// 获取所有可用的道具信息
    func getAllPropInfos() -> [PropInfo] {
        return Array(propInfoCache.values).sorted { $0.propId < $1.propId }
    }

    // MARK: - 私有方法

    /// 加载道具信息（从PopsInfo.json）
    private func loadPropInfos() {
        guard let url = Bundle.main.url(forResource: "PopsInfo", withExtension: "json"),
              let data = try? Data(contentsOf: url) else {
            print("❌ 无法加载PopsInfo.json文件")
            return
        }

        do {
            let propInfos = try jsonDecoder.decode([PropInfo].self, from: data)
            for propInfo in propInfos {
                propInfoCache[propInfo.propId] = propInfo
            }
            print("✅ 成功加载 \(propInfos.count) 个道具信息")
        } catch {
            print("❌ 解析PopsInfo.json失败: \(error)")
        }
    }
}

// MARK: - API请求模型

/// 创建道具交互请求模型
struct CreatePropInteractionRequest: Codable {
    let senderUserId: String
    let receiverUserId: String
    let propId: Int
    let remark: String?
}

/// 修改已读状态请求模型
struct UpdatePropInteractionRequest: Codable {
    let interactionId: String
    let userId: String
    let isRead: Bool
    let receivedTime: String? // ISO字符串格式
}

// MARK: - API响应模型

/// 通用API响应模型
struct PropInteractionAPIResponse<T: Codable>: Codable {
    let success: Bool
    let message: String
    let data: T?
}

/// 发送统计响应数据模型
struct PropInteractionStatsData: Codable {
    let stats: PropInteractionStats
    let readInteractions: [PropInteraction]
    let unreadInteractions: [PropInteraction]
}

/// 发送统计模型
struct PropInteractionStats: Codable {
    let total: Int
    let read: Int
    let unread: Int
}

// MARK: - 错误处理

/// 道具交互错误类型
enum PropInteractionError: LocalizedError {
    case invalidURL
    case noData
    case decodingError(Error)
    case networkError(Error)
    case serverError(String)
    case missingParameters
    case unauthorized
    case notFound
    case alreadyRead
    case cannotWithdraw

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的URL"
        case .noData:
            return "没有数据"
        case .decodingError(let error):
            return "数据解析错误: \(error.localizedDescription)"
        case .networkError(let error):
            return "网络错误: \(error.localizedDescription)"
        case .serverError(let message):
            return "服务器错误: \(message)"
        case .missingParameters:
            return "缺少必要参数"
        case .unauthorized:
            return "无权限执行此操作"
        case .notFound:
            return "交互记录不存在"
        case .alreadyRead:
            return "对方已查看，无法撤回"
        case .cannotWithdraw:
            return "无法撤回此交互"
        }
    }
}
