//
//  TextFieldStyle.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

struct TextFieldModifier: ViewModifier {
    func body(content: Content) -> some View {
        content
            .font(.bodyBrand)
            .foregroundColor(.textPrimary)
            .padding(Theme.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.cardBackground)
                    .overlay(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                            .stroke(Color.textSecondary.opacity(0.3), lineWidth: 1)
                    )
            )
            .textFieldStyle(PlainTextFieldStyle()) // 使用纯净样式避免默认边框
    }
}

extension View {
    func TextFieldBG() -> some View {
        self.modifier(TextFieldModifier())
    }
}
