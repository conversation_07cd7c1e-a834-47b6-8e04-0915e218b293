//
//  CardButtonStyle.swift
//  CarbonCoin
//
//  Created by Augment Agent on 2025-08-17.
//

import SwiftUI

// MARK: - 卡片按压样式
struct CardButtonStyle: ViewModifier {
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: isPressed)
            .onTapGesture {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .light)
                impactFeedback.impactOccurred()
            }
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

// MARK: - 高级卡片按压样式（带阴影变化）
struct AdvancedCardButtonStyle: ViewModifier {
    @State private var isPressed = false
    
    func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .shadow(
                color: Color.black.opacity(isPressed ? 0.1 : 0.15),
                radius: isPressed ? 4 : 8,
                x: 0,
                y: isPressed ? 2 : 4
            )
            .animation(.easeInOut(duration: 0.15), value: isPressed)
            .onTapGesture {
                // 触觉反馈
                let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
                impactFeedback.impactOccurred()
            }
            .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                isPressed = pressing
            }, perform: {})
    }
}

// MARK: - View Extension
extension View {
    /// 应用基础卡片按压效果
    func cardButtonStyle() -> some View {
        self.modifier(CardButtonStyle())
    }
    
    /// 应用高级卡片按压效果（带阴影变化）
    func advancedCardButtonStyle() -> some View {
        self.modifier(AdvancedCardButtonStyle())
    }
}

// MARK: - 预览
struct CardButtonStyle_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 基础样式预览
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.primary)
                .frame(width: 200, height: 100)
                .overlay(
                    Text("基础按压效果")
                        .foregroundColor(.white)
                        .font(.headline)
                )
                .cardButtonStyle()
            
            // 高级样式预览
            RoundedRectangle(cornerRadius: 16)
                .fill(LinearGradient(
                    colors: [Color(hex: "61D7B9"), Color(hex: "B0EB67")],
                    startPoint: .top,
                    endPoint: .bottom
                ))
                .frame(width: 200, height: 100)
                .overlay(
                    Text("高级按压效果")
                        .foregroundColor(.white)
                        .font(.headline)
                )
                .advancedCardButtonStyle()
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
