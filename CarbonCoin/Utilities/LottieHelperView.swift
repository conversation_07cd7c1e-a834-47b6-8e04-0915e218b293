//
//  LottieHelperView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/4.
//

import SwiftUI
import Lottie

struct LottieHelperView: View {
    var fileName: String = "success.json"
    var contentMode: UIView.ContentMode = .scaleAspectFill
    var playLoopMode: LottieLoopMode = .playOnce
    var animationProgress: AnimationProgressTime = 1
    
    var onAnimationDidFinish : (() -> Void)? = nil
    
    
    var body: some View {
        LottieView(animation: .named(fileName))
            .configure({ lottieAnimationView in
                lottieAnimationView.contentMode = contentMode
            })
            .playbackMode(.playing(.toProgress(animationProgress, loopMode: playLoopMode)))
            .animationDidFinish{completed in
                onAnimationDidFinish?()
            }
    }
}

#Preview {
    LottieHelperView()
}
