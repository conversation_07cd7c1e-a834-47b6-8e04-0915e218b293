//
//  DragManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import Foundation
import SwiftUI
import CoreLocation

// MARK: - 拖拽管理器

/// 拖拽管理器，用于处理卡片和道具的拖拽逻辑
struct DragManager {
    
    /// 处理项目拖放事件
    /// - Parameters:
    ///   - itemId: 拖放的项目ID（可能包含前缀）
    ///   - userId: 接收者的用户ID
    ///   - onCardDrop: 卡片拖放处理回调
    ///   - onPropDrop: 道具拖放处理回调
    static func handleItemDrop(
        itemId: String, 
        userId: String,
        onCardDrop: @escaping (String, String) -> Void,
        onPropDrop: @escaping (Int, String) -> Void
    ) {
        // 通过前缀明确区分卡片和道具
        if itemId.hasPrefix("prop_") {
            // 是道具ID，移除前缀后转换为整数
            let propIdString = String(itemId.dropFirst(5)) // 移除 "prop_" 前缀
            if let propId = Int(propIdString) {
                onPropDrop(propId, userId)
            }
        } else if itemId.hasPrefix("card_") {
            // 是卡片ID，移除前缀后传递
            let cardId = String(itemId.dropFirst(5)) // 移除 "card_" 前缀
            onCardDrop(cardId, userId)
        } else {
            // 为了兼容旧格式，保留原来的判断逻辑
            if let propId = Int(itemId) {
                // 是道具ID（纯数字）
                onPropDrop(propId, userId)
            } else {
                // 是卡片ID（非纯数字）
                onCardDrop(itemId, userId)
            }
        }
    }
    
    /// 创建带前缀的道具ID用于拖拽
    /// - Parameter propId: 道具ID
    /// - Returns: 带前缀的道具ID字符串
    static func createDraggablePropId(_ propId: Int) -> String {
        return "prop_\(propId)"
    }
    
    /// 创建带前缀的卡片ID用于拖拽
    /// - Parameter cardId: 卡片ID
    /// - Returns: 带前缀的卡片ID字符串
    static func createDraggableCardId(_ cardId: String) -> String {
        return "card_\(cardId)"
    }
    
    /// 从带前缀的ID中提取道具ID
    /// - Parameter prefixedId: 带前缀的ID
    /// - Returns: 道具ID，如果无法解析则返回nil
    static func extractPropId(from prefixedId: String) -> Int? {
        if prefixedId.hasPrefix("prop_") {
            let propIdString = String(prefixedId.dropFirst(5)) // 移除 "prop_" 前缀
            return Int(propIdString)
        } else if let propId = Int(prefixedId) {
            // 兼容旧格式（纯数字）
            return propId
        }
        return nil
    }
    
    /// 从带前缀的ID中提取卡片ID
    /// - Parameter prefixedId: 带前缀的ID
    /// - Returns: 卡片ID，如果无法解析则返回原始ID
    static func extractCardId(from prefixedId: String) -> String {
        if prefixedId.hasPrefix("card_") {
            return String(prefixedId.dropFirst(5)) // 移除 "card_" 前缀
        }
        return prefixedId // 兼容旧格式
    }
}

// MARK: - 全局拖拽状态管理

/// 全局拖拽状态管理器
class GlobalDragManager: ObservableObject {
    // MARK: - 卡片拖拽状态
    @Published var isDraggingCard = false
    @Published var draggedCard: ItemCard?
    @Published var dragPosition: CGPoint = .zero
    @Published var dragStartPosition: CGPoint = .zero
    
    // MARK: - 道具拖拽状态
    @Published var isDraggingProp = false
    @Published var draggedProp: PropInfo?
    @Published var propDragPosition: CGPoint = .zero
    @Published var propDragStartPosition: CGPoint = .zero
    
    // MARK: - 卡片拖拽处理函数
    
    /// 开始全局拖拽
    func startGlobalDrag(card: ItemCard, startPosition: CGPoint) {
        isDraggingCard = true
        draggedCard = card
        dragStartPosition = startPosition
        dragPosition = startPosition
        print("🎯 开始全局拖拽: \(card.title)")
    }
    
    /// 更新拖拽位置
    func updateGlobalDrag(to position: CGPoint) {
        dragPosition = position
    }
    
    /// 结束全局拖拽
    func endGlobalDrag() {
        isDraggingCard = false
        draggedCard = nil
        dragPosition = .zero
        dragStartPosition = .zero
    }
    
    // MARK: - 道具拖拽处理函数
    
    /// 开始道具全局拖拽
    func startGlobalPropDrag(prop: PropInfo, startPosition: CGPoint) {
        isDraggingProp = true
        draggedProp = prop
        propDragStartPosition = startPosition
        propDragPosition = startPosition
        print("🎯 开始全局拖拽道具: \(prop.propTitle)")
    }
    
    /// 更新道具拖拽位置
    func updateGlobalPropDrag(to position: CGPoint) {
        propDragPosition = position
    }
    
    /// 结束道具全局拖拽
    func endGlobalPropDrag() {
        isDraggingProp = false
        draggedProp = nil
        propDragPosition = .zero
        propDragStartPosition = .zero
    }
    
    /// 处理道具拖放到好友位置
    /// - Parameters:
    ///   - propId: 道具ID
    ///   - userId: 接收者用户ID
    ///   - onPropDrop: 道具拖放处理回调
    func handlePropDropToFriend(
        propId: Int,
        userId: String,
        onPropDrop: @escaping (Int, String) -> Void
    ) {
        print("🎯 道具 \(propId) 被拖放到好友 \(userId)")
        onPropDrop(propId, userId)
    }
}