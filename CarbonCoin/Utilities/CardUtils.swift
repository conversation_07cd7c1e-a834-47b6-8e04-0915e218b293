//
//  CardUtils.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/7.
//

import Foundation
import SwiftUI

// MARK: - 卡片工具类
/// 提供卡片相关的通用方法和工具函数
class CardUtils {
    
    // MARK: - 卡片类型相关
    
    /// 获取卡片类型的显示图标
    /// - Parameter cardType: 卡片类型
    /// - Returns: 对应的SF Symbol图标名称
    static func getCardTypeIcon(for cardType: CardType) -> String {
        switch cardType {
        case .scenery:
            return "photo.on.rectangle"
        case .shopping:
            return "bag.fill"
        }
    }
    
    /// 获取卡片类型的默认主题色
    /// - Parameter cardType: 卡片类型
    /// - Returns: 默认主题色的hex字符串
    static func getDefaultThemeColor(for cardType: CardType) -> String? {
        switch cardType {
        case .scenery:
            return nil // 风景卡片不使用主题色
        case .shopping:
            return "4B7905" // 购物卡片默认绿色主题
        }
    }
    
    /// 获取卡片类型的默认奖励
    /// - Parameter cardType: 卡片类型
    /// - Returns: (碳币奖励, 经验奖励)
    static func getDefaultRewards(for cardType: CardType) -> (coinReward: Int, experienceReward: Int) {
        switch cardType {
        case .scenery:
            return (coinReward: 10, experienceReward: 5)
        case .shopping:
            return (coinReward: 15, experienceReward: 8)
        }
    }
    
    // MARK: - 卡片验证
    
    /// 验证卡片数据的完整性
    /// - Parameters:
    ///   - title: 卡片标题
    ///   - description: 卡片描述
    ///   - cardType: 卡片类型
    ///   - themeColor: 主题色（购物卡片必需）
    /// - Returns: 验证结果，成功返回nil，失败返回错误信息
    static func validateCardData(
        title: String,
        description: String,
        cardType: CardType,
        themeColor: String?
    ) -> String? {
        // 验证标题
        if title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "卡片标题不能为空"
        }
        
        if title.count > 50 {
            return "卡片标题不能超过50个字符"
        }
        
        // 验证描述
        if description.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            return "卡片描述不能为空"
        }
        
        if description.count > 500 {
            return "卡片描述不能超过500个字符"
        }
        
        // 验证购物卡片的主题色
        if cardType == .shopping {
            guard let themeColor = themeColor,
                  !themeColor.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
                return "购物卡片必须设置主题色"
            }
            
            // 验证hex颜色格式
            if !isValidHexColor(themeColor) {
                return "主题色格式不正确，请使用有效的hex颜色值"
            }
        }
        
        return nil
    }
    
    /// 验证hex颜色格式
    /// - Parameter hex: hex颜色字符串
    /// - Returns: 是否为有效的hex颜色
    static func isValidHexColor(_ hex: String) -> Bool {
        let hex = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        let pattern = "^#?([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: hex.utf16.count)
        return regex?.firstMatch(in: hex, options: [], range: range) != nil
    }
    
    // MARK: - 卡片显示相关
    
    /// 格式化奖励显示文本
    /// - Parameters:
    ///   - coinReward: 碳币奖励
    ///   - experienceReward: 经验奖励
    /// - Returns: 格式化的奖励文本
    static func formatRewardsText(coinReward: Int, experienceReward: Int) -> String {
        var rewards: [String] = []
        
        if coinReward > 0 {
            rewards.append("\(coinReward) 碳币")
        }
        
        if experienceReward > 0 {
            rewards.append("\(experienceReward) 经验")
        }
        
        return rewards.isEmpty ? "无奖励" : rewards.joined(separator: " + ")
    }
    
    /// 获取卡片背景渐变色
    /// - Parameters:
    ///   - cardType: 卡片类型
    ///   - themeColor: 主题色
    /// - Returns: SwiftUI渐变色
    static func getCardBackgroundGradient(cardType: CardType, themeColor: String?) -> LinearGradient {
        switch cardType {
        case .scenery:
            // 风景卡片使用默认的品牌渐变
            return LinearGradient.primaryBrand
        case .shopping:
            // 购物卡片使用主题色渐变
            if let themeColor = themeColor {
                let baseColor = Color(hex: themeColor)
                let lighterColor = baseColor.opacity(0.7)
                return LinearGradient(
                    gradient: Gradient(colors: [baseColor, lighterColor]),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            } else {
                // 如果没有主题色，使用默认渐变
                return LinearGradient.primaryBrand
            }
        }
    }
    
    // MARK: - 卡片权限检查
    
    /// 检查用户是否可以编辑卡片
    /// - Parameters:
    ///   - userItemCard: 用户卡片
    ///   - currentUserId: 当前用户ID
    /// - Returns: 是否可以编辑
    static func canEditCard(userItemCard: UserItemCard, currentUserId: String) -> Bool {
        return userItemCard.isAuthor && userItemCard.userId == currentUserId
    }
    
    /// 检查用户是否可以删除卡片
    /// - Parameters:
    ///   - userItemCard: 用户卡片
    ///   - currentUserId: 当前用户ID
    /// - Returns: 是否可以删除
    static func canDeleteCard(userItemCard: UserItemCard, currentUserId: String) -> Bool {
        return userItemCard.userId == currentUserId
    }
}
