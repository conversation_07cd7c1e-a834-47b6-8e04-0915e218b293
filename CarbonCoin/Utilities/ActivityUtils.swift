//
//  ActivityUtils.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/7.
//

import Foundation

// MARK: 活动相关辅助函数
struct ActivityUtils {
    
    /// 获取活动类型显示名称
    static func getDisplayName(for activityType: String) -> String {
        switch activityType.lowercased() {
        case "walking":
            return "步行"
        case "cycling":
            return "骑行"
        case "bus":
            return "公交"
        case "subway":
            return "地铁"
        default:
            return "出行"
        }
    }
    
    /// 获取公开日志中的活动图标
    static func getPublicRecordIconName(for log: UserLog) -> String {
        switch log.recordType {
        case .location:
            return "log-checkin"
        case .recognition:
            return "log-shopping"
        case .trip:
            // 足迹记录需要根据活动类型选择图标
            if let footprint = log.userFootprints {
                switch footprint.activityType.rawValue {
                case "walking":
                    return "log-walk"
                case "cycling":
                    return "log-cycling"
                case "bus":
                    return "log-bus"
                case "subway":
                    return "log-subway"
                default:
                    return "log-walk" // 默认使用步行图标
                }
            }
            return "log-walk"
        }
    }
    
    /// 获取全部日志中的图标
    static func getIconName(for log: UserLog) -> String {
        if log.isPublic {
            return "log-public"
        }
        
        return getPublicRecordIconName(for: log)
    }
}

