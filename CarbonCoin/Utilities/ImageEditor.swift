//
//  ImageEditor.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI
import CropViewController  // Swift 封装的 TOCropViewController
import UIKit
import CoreImage
import CoreImage.CIFilterBuiltins


// MARK: 图片裁切工具
struct ImageEditor {
    /// 返回一个可以裁剪图片的 SwiftUI View
    static func cropper(image: UIImage, completion: @escaping (UIImage?) -> Void) -> some View {
        CropperSheet(image: image, onComplete: completion)
    }

    private struct CropperSheet: UIViewControllerRepresentable {
        let image: UIImage
        let onComplete: (UIImage?) -> Void
        @Environment(\.presentationMode) var presentationMode

        func makeCoordinator() -> Coordinator {
            Coordinator(self)
        }

        func makeUIViewController(context: Context) -> CropViewController {
            let cropVC = CropViewController(image: image)
            cropVC.delegate = context.coordinator
            cropVC.aspectRatioPickerButtonHidden = true
            cropVC.rotateButtonsHidden = false
            cropVC.aspectRatioLockEnabled = false
            cropVC.resetButtonHidden = false
            cropVC.cropView.cropBoxResizeEnabled = true
            return cropVC
        }

        func updateUIViewController(_ uiViewController: CropViewController, context: Context) {}

        class Coordinator: NSObject, CropViewControllerDelegate {
            let parent: CropperSheet
            init(_ parent: CropperSheet) { self.parent = parent }

            func cropViewController(_ cropViewController: CropViewController,
                                    didCropToImage image: UIImage,
                                    withRect: CGRect,
                                    angle: Int) {
                parent.onComplete(image)
                parent.presentationMode.wrappedValue.dismiss()
            }

            func cropViewController(_ cropViewController: CropViewController,
                                    didFinishCancelled cancelled: Bool) {
                parent.onComplete(nil)
                parent.presentationMode.wrappedValue.dismiss()
            }
        }
    }
}


// MARK: 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Environment(\.presentationMode) var presentationMode
    
    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }
    
    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}
    
    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }
    
    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker
        
        init(_ parent: ImagePicker) {
            self.parent = parent
        }
        
        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}


// MARK: 单色图像
extension UIImage {
    /// 生成单色版本的图像（保留 alpha 通道，非透明像素填充为指定颜色）
    func monochromeImage(with color: UIColor) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, scale)
        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        
        context.translateBy(x: 0, y: size.height)
        context.scaleBy(x: 1, y: -1)
        
        // 绘制原始图像以获取 alpha 蒙版
        let rect = CGRect(origin: .zero, size: size)
        context.clip(to: rect, mask: cgImage!)
        
        // 填充指定颜色
        color.setFill()
        context.fill(rect)
        
        let monochromeImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return monochromeImage
    }
}



// MARK: 增加图片的padding
extension UIImage {
    func paddedImage(padding: CGFloat) -> UIImage? {
        let newSize = CGSize(width: size.width + padding*2, height: size.height + padding*2)
        UIGraphicsBeginImageContextWithOptions(newSize, false, scale)
        self.draw(at: CGPoint(x: padding, y: padding))
        let padded = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return padded
    }
}

