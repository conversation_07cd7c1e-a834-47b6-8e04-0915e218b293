//
//  DateParsingExtension.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/4.
//

import Foundation

// MARK: - 日期解析扩展

extension String {
    /// 解析ISO8601格式的时间戳字符串，支持多种格式
    /// 支持的格式：
    /// - yyyy-MM-dd'T'HH:mm:ss.SSSZ (带毫秒)
    /// - yyyy-MM-dd'T'HH:mm:ssZ (不带毫秒)
    /// - ISO8601DateFormatter 标准格式
    func parseISO8601Date() -> Date? {
        // 创建支持多种ISO8601格式的日期解析器
        let dateFormatter = DateFormatter()
        dateFormatter.locale = Locale(identifier: "en_US_POSIX")
        dateFormatter.timeZone = TimeZone(secondsFromGMT: 0)
        
        // 首先尝试带毫秒的格式
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        if let date = dateFormatter.date(from: self) {
            return date
        }
        
        // 如果失败，尝试不带毫秒的格式
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ssZ"
        if let date = dateFormatter.date(from: self) {
            return date
        }
        
        // 如果还是失败，尝试ISO8601DateFormatter（带毫秒）
        let iso8601Formatter = ISO8601DateFormatter()
        iso8601Formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        if let date = iso8601Formatter.date(from: self) {
            return date
        }
        
        // 最后尝试标准ISO8601格式（不带毫秒）
        let standardISO8601Formatter = ISO8601DateFormatter()
        if let date = standardISO8601Formatter.date(from: self) {
            return date
        }
        
        // 所有格式都失败
        print("⚠️ 无法解析时间戳格式: \(self)")
        return nil
    }
}
