//
//  Date.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import Foundation

// MARK: - Date 扩展
extension Date {
    
    /// 获取日期的显示格式
    func displayString(for period: TimePeriod) -> String {
        let formatter = DateFormatter()
        
        switch period {
        case .day:
            formatter.dateFormat = "HH:mm"
        case .week, .month:
            formatter.dateFormat = "MM/dd"
        case .sixMonths:
            formatter.dateFormat = "MM月"
        }
        
        return formatter.string(from: self)
    }
    
    /// 获取当前时间段内的所有日期点
    static func datePoints(for period: TimePeriod) -> [Date] {
        let calendar = Calendar.current
        let now = Date()
        let startDate = period.currentPeriodStartDate
        
        var dates: [Date] = []
        var currentDate = startDate
        
        while currentDate <= now {
            dates.append(currentDate)
            currentDate = calendar.date(byAdding: period.aggregationInterval, value: 1, to: currentDate) ?? now
        }
        
        return dates
    }
}

// MARK: - Calendar 扩展
extension Calendar {
    
    /// 获取指定日期的开始时间
    func startOfDay(for date: Date) -> Date {
        return self.dateInterval(of: .day, for: date)?.start ?? date
    }
    
    /// 获取指定日期的结束时间
    func endOfDay(for date: Date) -> Date {
        guard let interval = self.dateInterval(of: .day, for: date) else { return date }
        return interval.end.addingTimeInterval(-1)
    }
}


// MARK: 由String转换得到日期
extension String {
    func toDate() -> Date? {
        let formatter = ISO8601DateFormatter()
        formatter.formatOptions = [.withInternetDateTime, .withFractionalSeconds]
        return formatter.date(from: self)
    }
}
