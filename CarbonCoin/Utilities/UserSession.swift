//
//  UserSession.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation

func clearDocumentsDirectory() {
    let fileManager = FileManager.default
    guard let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else {
        return
    }
    
    do {
        let fileURLs = try fileManager.contentsOfDirectory(
            at: documentsURL,
            includingPropertiesForKeys: nil,
            options: .skipsHiddenFiles
        )
        for url in fileURLs {
            try fileManager.removeItem(at: url)
        }
        print("✅ Documents 目录已清空所有内容")
    } catch {
        print("清理 Documents 目录失败：\(error)")
    }
}
