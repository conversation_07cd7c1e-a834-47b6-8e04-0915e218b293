//
//  PropService.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import Foundation

// MARK: - 道具服务协议

/// 道具交互服务协议
protocol PropServiceProtocol {
    /// 创建道具交互
    func createPropInteraction(senderUserId: String, receiverUserId: String, propId: Int, remark: String?) async throws -> PropInteraction

    /// 撤回道具交互（删除）
    func deletePropInteraction(interactionId: String, userId: String) async throws

    /// 修改已读状态
    func updateReadStatus(interactionId: String, userId: String, isRead: Bool, receivedTime: Date?) async throws -> PropInteraction

    /// 查询发送统计
    func getSendingStats(userId: String) async throws -> PropInteractionStatsData

    /// 查询未读信息
    func getUnreadInteractions(userId: String) async throws -> [PropInteraction]
}

// MARK: - 道具服务实现

/// 道具交互服务实现类
class PropService: PropServiceProtocol {

    // MARK: - 属性

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - 初始化

    init() {
        setupDateCoding()
    }

    // MARK: - 私有方法

    /// 设置日期编码格式
    private func setupDateCoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 创建通用网络请求
    private func performRequest<T: Codable>(
        endpoint: String,
        method: String = "GET",
        body: Data? = nil,
        queryItems: [URLQueryItem]? = nil
    ) async throws -> PropInteractionAPIResponse<T> {
        // 构建URL
        var urlComponents = URLComponents(string: baseURL + endpoint)
        if let queryItems = queryItems {
            urlComponents?.queryItems = queryItems
        }

        guard let url = urlComponents?.url else {
            throw PropInteractionError.invalidURL
        }

        print("🌐 道具交互请求: \(method) \(url.absoluteString)")

        // 创建请求
        var request = URLRequest(url: url)
        request.httpMethod = method
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = 30.0

        if let body = body {
            request.httpBody = body
            if let bodyString = String(data: body, encoding: .utf8) {
                print("📤 请求体: \(bodyString)")
            }
        }

        // 执行请求
        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw PropInteractionError.networkError(URLError(.badServerResponse))
            }

            print("📥 响应状态码: \(httpResponse.statusCode)")

            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 响应内容: \(responseString)")
            }

            // 检查HTTP状态码
            switch httpResponse.statusCode {
            case 200...299:
                break
            case 400:
                throw PropInteractionError.missingParameters
            case 403:
                throw PropInteractionError.unauthorized
            case 404:
                throw PropInteractionError.notFound
            default:
                let errorMessage = String(data: data, encoding: .utf8) ?? "未知错误"
                throw PropInteractionError.serverError(errorMessage)
            }

            // 解析响应
            do {
                let apiResponse = try jsonDecoder.decode(PropInteractionAPIResponse<T>.self, from: data)
                return apiResponse
            } catch {
                print("❌ 响应解析失败: \(error)")
                throw PropInteractionError.decodingError(error)
            }

        } catch let error as PropInteractionError {
            throw error
        } catch {
            print("❌ 网络请求失败: \(error)")
            throw PropInteractionError.networkError(error)
        }
    }

    // MARK: - 公共API方法

    /// 创建道具交互
    func createPropInteraction(senderUserId: String, receiverUserId: String, propId: Int, remark: String?) async throws -> PropInteraction {
        let request = CreatePropInteractionRequest(
            senderUserId: senderUserId,
            receiverUserId: receiverUserId,
            propId: propId,
            remark: remark
        )

        let requestData = try jsonEncoder.encode(request)
        let response: PropInteractionAPIResponse<PropInteraction> = try await performRequest(
            endpoint: "prop-interaction",
            method: "POST",
            body: requestData
        )

        guard let interaction = response.data else {
            throw PropInteractionError.noData
        }

        print("✅ 道具交互创建成功: \(interaction.id)")
        return interaction
    }

    /// 撤回道具交互（删除）
    func deletePropInteraction(interactionId: String, userId: String) async throws {
        let queryItems = [
            URLQueryItem(name: "interactionId", value: interactionId),
            URLQueryItem(name: "userId", value: userId)
        ]

        let response: PropInteractionAPIResponse<String> = try await performRequest(
            endpoint: "prop-interaction",
            method: "DELETE",
            queryItems: queryItems
        )

        if !response.success {
            throw PropInteractionError.serverError(response.message)
        }

        print("✅ 道具交互撤回成功: \(interactionId)")
    }

    /// 修改已读状态
    func updateReadStatus(interactionId: String, userId: String, isRead: Bool, receivedTime: Date?) async throws -> PropInteraction {
        let receivedTimeString = receivedTime?.ISO8601Format()
        let request = UpdatePropInteractionRequest(
            interactionId: interactionId,
            userId: userId,
            isRead: isRead,
            receivedTime: receivedTimeString
        )

        let requestData = try jsonEncoder.encode(request)
        let response: PropInteractionAPIResponse<PropInteraction> = try await performRequest(
            endpoint: "prop-interaction",
            method: "PATCH",
            body: requestData
        )

        guard let interaction = response.data else {
            throw PropInteractionError.noData
        }

        print("✅ 已读状态更新成功: \(interaction.id), isRead: \(isRead)")
        return interaction
    }

    /// 查询发送统计
    func getSendingStats(userId: String) async throws -> PropInteractionStatsData {
        let queryItems = [
            URLQueryItem(name: "userId", value: userId)
        ]

        let response: PropInteractionAPIResponse<PropInteractionStatsData> = try await performRequest(
            endpoint: "prop-interaction/stats",
            method: "GET",
            queryItems: queryItems
        )

        guard let statsData = response.data else {
            throw PropInteractionError.noData
        }

        print("✅ 发送统计查询成功: 总计 \(statsData.stats.total), 已读 \(statsData.stats.read), 未读 \(statsData.stats.unread)")
        return statsData
    }

    /// 查询未读信息
    func getUnreadInteractions(userId: String) async throws -> [PropInteraction] {
        let queryItems = [
            URLQueryItem(name: "userId", value: userId)
        ]

        let response: PropInteractionAPIResponse<[PropInteraction]> = try await performRequest(
            endpoint: "prop-interaction",
            method: "GET",
            queryItems: queryItems
        )

        guard let interactions = response.data else {
            throw PropInteractionError.noData
        }

        print("✅ 未读信息查询成功: 共 \(interactions.count) 条未读消息")
        return interactions
    }
}

// MARK: - 单例访问

extension PropService {
    /// 共享实例
    static let shared = PropService()
}
