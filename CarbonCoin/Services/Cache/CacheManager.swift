//
//  CacheManager.swift
//  CarbonCoin
//
//  单例缓存管理器：负责健康数据的本地缓存、过期判断与清理
//  Created by <PERSON><PERSON> on 2025/8/20.
//

import Foundation

// MARK: - 缓存键辅助
private enum CacheKeyHelper {
    /// 构造缓存键：health.<type>.<period>
    static func key(for type: HealthDataType, period: TimePeriod) -> String {
        return "health.\(type.rawValue).\(period.rawValue)"
    }
}

// MARK: - 缓存时长策略（毫秒级判断用秒计算即可）
private enum CacheTTL: TimeInterval {
    /// 日数据缓存1小时
    case day = 3600
    /// 周数据缓存4小时
    case week = 14400
    /// 月数据缓存12小时
    case month = 43200
    /// 半年数据缓存24小时
    case sixMonths = 86400
    
    static func ttl(for period: TimePeriod) -> TimeInterval {
        switch period {
        case .day: return CacheTTL.day.rawValue
        case .week: return CacheTTL.week.rawValue
        case .month: return CacheTTL.month.rawValue
        case .sixMonths: return CacheTTL.sixMonths.rawValue
        }
    }
}

// MARK: - CacheManager
/// 负责健身数据的读取、写入、过期判断与清理
final class CacheManager {
    /// 单例实例
    static let shared = CacheManager()
    private init() {}
    
    /// UserDefaults 实例（可扩展为 AppGroup）
    private let defaults = UserDefaults.standard
    
    // MARK: - 写入缓存
    /// 写入缓存（覆盖同键值）
    /// - Parameters:
    ///   - data: 健康数据数组
    ///   - type: 健康数据类型
    ///   - period: 时间段
    func save(data: [HealthData], type: HealthDataType, period: TimePeriod) {
        let cached = CachedHealthData(data: data, type: type, period: period, cachedAt: Date())
        let key = CacheKeyHelper.key(for: type, period: period)
        do {
            let encoded = try JSONEncoder().encode(cached)
            defaults.set(encoded, forKey: key)
        } catch {
            // 编码失败不应影响主流程
            print("[CacheManager] save encode error: \(error)")
        }
    }
    
    // MARK: - 读取缓存（不判断过期）
    private func readRaw(type: HealthDataType, period: TimePeriod) -> CachedHealthData? {
        let key = CacheKeyHelper.key(for: type, period: period)
        guard let data = defaults.data(forKey: key) else { return nil }
        do {
            return try JSONDecoder().decode(CachedHealthData.self, from: data)
        } catch {
            print("[CacheManager] read decode error: \(error)")
            return nil
        }
    }
    
    // MARK: - 判断是否过期
    private func isExpired(_ cached: CachedHealthData) -> Bool {
        let ttl = CacheTTL.ttl(for: cached.period)
        return Date().timeIntervalSince(cached.cachedAt) > ttl
    }
    
    // MARK: - 公共读取：带过期判断
    /// 返回缓存命中与数据；若过期则返回数据但标记为过期（以便上层继续异步刷新）
    func read(type: HealthDataType, period: TimePeriod) -> (hit: Bool, expired: Bool, data: [HealthData]) {
        guard let cached = readRaw(type: type, period: period) else {
            return (false, false, [])
        }
        let expired = isExpired(cached)
        return (true, expired, cached.data)
    }
    
    // MARK: - 清理指定键
    func remove(type: HealthDataType, period: TimePeriod) {
        defaults.removeObject(forKey: CacheKeyHelper.key(for: type, period: period))
    }
    
    // MARK: - 过期清理（全量）
    /// 简单实现：遍历所有组合键（类型 x 时间段）并清理过期
    func cleanupExpired() {
        for type in HealthDataType.allCases {
            for period in TimePeriod.allCases {
                if let cached = readRaw(type: type, period: period), isExpired(cached) {
                    remove(type: type, period: period)
                }
            }
        }
    }
}

