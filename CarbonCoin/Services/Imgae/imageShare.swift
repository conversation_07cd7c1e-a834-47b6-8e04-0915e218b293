//
//  imageShare.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import Foundation
import UIKit

// MARK: - 图片上传响应模型
struct ImageUploadResponse: Codable {
    let success: Bool
    let message: String
    let data: ImageUploadData?
}

struct ImageUploadData: Codable {
    let url: String
    let filename: String
}

// MARK: - 图片上传服务
class ImageShareService: ObservableObject {
    static let shared = ImageShareService()
    private let baseURL = AuthConfig.baseURL

    private init() {}

    // MARK: 上传图片
    // 上传图片到后端并获取URL
    func uploadImage(_ imageData: Data) async throws -> String {
        guard let url = URL(string: "\(baseURL)/upload/image") else {
            throw ImageServiceError.invalidURL
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"

        // 创建multipart/form-data请求体
        let boundary = "Boundary-\(UUID().uuidString)"
        request.setValue("multipart/form-data; boundary=\(boundary)", forHTTPHeaderField: "Content-Type")

        let httpBody = createMultipartBody(imageData: imageData, boundary: boundary)
        request.httpBody = httpBody

        do {
            let (data, response) = try await URLSession.shared.data(for: request)

            // 检查HTTP响应状态
            if let httpResponse = response as? HTTPURLResponse {
                print("图片上传响应状态码: \(httpResponse.statusCode)")

                guard httpResponse.statusCode == 200 else {
                    throw ImageServiceError.serverError(httpResponse.statusCode)
                }
            }

            // 解析响应数据
            let uploadResponse = try JSONDecoder().decode(ImageUploadResponse.self, from: data)

            if uploadResponse.success, let imageURL = uploadResponse.data?.url {
                print("图片上传成功，URL: \(imageURL)")
                return imageURL
            } else {
                throw ImageServiceError.uploadFailed(uploadResponse.message)
            }

        } catch let error as DecodingError {
            print("图片上传响应解析失败: \(error)")
            throw ImageServiceError.decodingError
        } catch let error as ImageServiceError {
            throw error
        } catch {
            print("图片上传网络请求失败: \(error)")
            throw ImageServiceError.networkError(error)
        }
    }
    
    // MARK: 删除COS中的图片
    func deleteImage(_ filename: String) async throws -> () {
        guard let url = URL(string: "\(baseURL)/upload/image") else{
            throw ImageServiceError.invalidURL
        }
        
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let body: [String: String] = ["filename": filename]
        request.httpBody = try JSONSerialization.data(withJSONObject: body)
        
        do {
            let (_, response) = try await URLSession.shared.data(for: request)

            if let httpResponse = response as? HTTPURLResponse {
                print("图片删除响应状态码: \(httpResponse.statusCode)")
                guard httpResponse.statusCode == 200 else {
                    throw ImageServiceError.serverError(httpResponse.statusCode)
                }
            }
            // 不需要解析返回数据
        } catch {
            print("图片删除请求失败: \(error)")
            throw ImageServiceError.networkError(error)
        }
    }

    // 创建multipart/form-data请求体
    private func createMultipartBody(imageData: Data, boundary: String) -> Data {
        var body = Data()

        // 添加图片数据
        body.append("--\(boundary)\r\n".data(using: .utf8)!)
        body.append("Content-Disposition: form-data; name=\"image\"; filename=\"image.png\"\r\n".data(using: .utf8)!)
        body.append("Content-Type: image/png\r\n\r\n".data(using: .utf8)!)
        body.append(imageData)
        body.append("\r\n".data(using: .utf8)!)

        // 结束边界
        body.append("--\(boundary)--\r\n".data(using: .utf8)!)

        return body
    }
}

// MARK: - 图片上传错误类型
enum ImageServiceError: LocalizedError {
    case invalidURL
    case networkError(Error)
    case serverError(Int)
    case uploadFailed(String)
    case decodingError

    var errorDescription: String? {
        switch self {
        case .invalidURL:
            return "无效的上传URL"
        case .networkError(let error):
            return "网络请求失败: \(error.localizedDescription)"
        case .serverError(let code):
            return "服务器错误，状态码: \(code)"
        case .uploadFailed(let message):
            return "上传失败: \(message)"
        case .decodingError:
            return "响应数据解析失败"
        }
    }
}
