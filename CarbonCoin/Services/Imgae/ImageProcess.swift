//
//  ImageProcess.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/22.
//

import UIKit
import Vision
import CoreImage
import CoreImage.CIFilterBuiltins

class ImageProcess {
    // MARK: - 主体识别
    func detectSubjects(in image: UIImage) -> VNInstanceMaskObservation? {
        guard let ciImage = CIImage(image: image) else { return nil }
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage)
        do {
            try handler.perform([request])
            return request.results?.first
        } catch {
            print("主体识别失败: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 主体信息结构
    struct SubjectInfo {
        let index: Int
        let centerPosition: CGPoint // 标准化坐标 (0-1)
        let boundingBox: CGRect     // 标准化坐标 (0-1)
    }

    // MARK: - 计算所有主体的中心位置
    func calculateSubjectCenters(from observation: VNInstanceMaskObservation) -> [SubjectInfo] {
        let mask = observation.instanceMask
        let width = CVPixelBufferGetWidth(mask)
        let height = CVPixelBufferGetHeight(mask)

        CVPixelBufferLockBaseAddress(mask, .readOnly)
        defer { CVPixelBufferUnlockBaseAddress(mask, .readOnly) }

        guard let pixels = CVPixelBufferGetBaseAddress(mask) else {
            return []
        }

        let bytesPerRow = CVPixelBufferGetBytesPerRow(mask)
        let buffer = pixels.assumingMemoryBound(to: UInt8.self)

        // 为每个实例收集像素位置
        var instancePixels: [Int: [(x: Int, y: Int)]] = [:]

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = y * bytesPerRow + x
                let instanceLabel = Int(buffer[pixelIndex])

                if instanceLabel > 0 { // 跳过背景 (0)
                    if instancePixels[instanceLabel] == nil {
                        instancePixels[instanceLabel] = []
                    }
                    instancePixels[instanceLabel]?.append((x: x, y: y))
                }
            }
        }

        // 计算每个实例的中心位置和边界框
        var subjects: [SubjectInfo] = []

        for (instanceIndex, pixels) in instancePixels {
            guard !pixels.isEmpty else { continue }

            let minX = pixels.map { $0.x }.min() ?? 0
            let maxX = pixels.map { $0.x }.max() ?? 0
            let minY = pixels.map { $0.y }.min() ?? 0
            let maxY = pixels.map { $0.y }.max() ?? 0

            // 计算中心位置（标准化坐标）
            let centerX = Double(minX + maxX) / 2.0 / Double(width)
            let centerY = Double(minY + maxY) / 2.0 / Double(height)

            // 计算边界框（标准化坐标）
            let boundingBox = CGRect(
                x: Double(minX) / Double(width),
                y: Double(minY) / Double(height),
                width: Double(maxX - minX + 1) / Double(width),
                height: Double(maxY - minY + 1) / Double(height)
            )

            let subject = SubjectInfo(
                index: instanceIndex,
                centerPosition: CGPoint(x: centerX, y: centerY),
                boundingBox: boundingBox
            )

            subjects.append(subject)
        }

        return subjects.sorted { $0.index < $1.index }
    }

    // MARK: - 根据点击位置获取最近的主体
    func findNearestSubject(at point: CGPoint, in subjects: [SubjectInfo], threshold: Double = 0.1) -> SubjectInfo? {
        var nearestSubject: SubjectInfo?
        var minDistance = Double.infinity

        for subject in subjects {
            let distance = sqrt(
                pow(point.x - subject.centerPosition.x, 2) +
                pow(point.y - subject.centerPosition.y, 2)
            )

            if distance < threshold && distance < minDistance {
                minDistance = distance
                nearestSubject = subject
            }
        }

        return nearestSubject
    }

    // MARK: - 主体提取和裁切
    func generateOutput(
        forInputImage inputImage: CIImage,
        backgroundImage: CIImage,
        tapPosition: CGPoint?,
        croppedToInstancesExtent: Bool = true
    ) -> CIImage? {
        // 生成掩码
        guard let mask = subjectMask(fromImage: inputImage, atPoint: tapPosition) else {
            return nil
        }

        // 应用视觉效果
        return apply(
            mask: mask,
            toInputImage: inputImage,
            background: backgroundImage,
            croppedToInstancesExtent: croppedToInstancesExtent
        )
    }

    // MARK: 生成图像掩码Mask
    private func subjectMask(fromImage image: CIImage, atPoint point: CGPoint?) -> CIImage? {
        // 执行Vision请求
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: image)

        do {
            try handler.perform([request])
        } catch {
            return nil
        }

        guard let result = request.results?.first else { return nil }

        // 从点击获取实例索引
        let instances: IndexSet
        if let point = point {
            let subjects = calculateSubjectCenters(from: result)
            if let nearestSubject = findNearestSubject(at: point, in: subjects) {
                instances = IndexSet([nearestSubject.index])
            } else {
                instances = result.allInstances
            }
        } else {
            instances = result.allInstances
        }

        // 生成掩码
        do {
            let mask = try result.generateScaledMaskForImage(
                forInstances: instances,
                from: handler
            )
            return CIImage(cvPixelBuffer: mask)
        } catch {
            return nil
        }
    }

    
    // MARK: 利用image和mask定义图像
    private func apply(
        mask: CIImage,
        toInputImage inputImage: CIImage,
        background: CIImage? = nil,
        croppedToInstancesExtent: Bool
    ) -> CIImage? {
        let transparent = CIImage(color: .clear).cropped(to: inputImage.extent)

        let filter = CIFilter.blendWithMask()
        filter.inputImage = inputImage
        filter.maskImage = mask
        filter.backgroundImage = background ?? transparent

        guard let outputImage = filter.outputImage else { return nil }

        // 裁切到主体范围
        if croppedToInstancesExtent {
            return cropToSubjectExtent(outputImage, mask: mask)
        } else {
            return outputImage
        }
    }

    // MARK: 裁切图像
    private func cropToSubjectExtent(_ image: CIImage, mask: CIImage) -> CIImage {
        // 计算主体的实际边界框
        guard let subjectBounds = calculateSubjectBounds(from: mask) else {
            // 如果无法计算边界框，返回原图
            return image
        }

        // 裁切到主体边界
        return image.cropped(to: subjectBounds)
    }

    private func calculateSubjectBounds(from mask: CIImage) -> CGRect? {
        // 将CIImage转换为CGImage以便访问像素数据
        let context = CIContext()
        guard let cgImage = context.createCGImage(mask, from: mask.extent) else {
            return nil
        }

        let width = cgImage.width
        let height = cgImage.height

        // 创建位图上下文来读取像素数据
        guard let colorSpace = CGColorSpace(name: CGColorSpace.genericGrayGamma2_2),
              let bitmapContext = CGContext(
                data: nil,
                width: width,
                height: height,
                bitsPerComponent: 8,
                bytesPerRow: width,
                space: colorSpace,
                bitmapInfo: CGImageAlphaInfo.none.rawValue
              ) else {
            return nil
        }

        // 绘制掩码到位图上下文
        bitmapContext.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        guard let data = bitmapContext.data else {
            return nil
        }

        let pixels = data.assumingMemoryBound(to: UInt8.self)

        // 查找主体的边界
        var minX = width
        var maxX = 0
        var minY = height
        var maxY = 0
        var foundPixel = false

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = y * width + x
                let pixelValue = pixels[pixelIndex]

                // 检查是否为前景像素（非零值）
                if pixelValue > 0 {
                    foundPixel = true
                    minX = min(minX, x)
                    maxX = max(maxX, x)
                    minY = min(minY, y)
                    maxY = max(maxY, y)
                }
            }
        }

        guard foundPixel, minX <= maxX, minY <= maxY else {
            return nil
        }

        // 添加一些边距以避免过度裁切
        let padding: Int = 18
        let paddedMinX = max(0, minX - padding)
        let paddedMinY = max(0, minY - padding)
        let paddedMaxX = min(width - 1, maxX + padding)
        let paddedMaxY = min(height - 1, maxY + padding)

        // 转换为CIImage坐标系（Y轴翻转）
        let bounds = CGRect(
            x: CGFloat(paddedMinX),
            y: CGFloat(height - paddedMaxY - 1),
            width: CGFloat(paddedMaxX - paddedMinX + 1),
            height: CGFloat(paddedMaxY - paddedMinY + 1)
        )

        return bounds
    }

    // MARK: - 便捷方法：提取主体并保存
    func extractSubject(
        from image: UIImage,
        tapPosition: CGPoint? = nil,
        croppedToInstancesExtent: Bool = true
    ) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        // 使用透明背景
        let backgroundImage = CIImage.empty()

        guard let outputCIImage = generateOutput(
            forInputImage: ciImage,
            backgroundImage: backgroundImage,
            tapPosition: tapPosition,
            croppedToInstancesExtent: croppedToInstancesExtent
        ) else {
            return nil
        }

        return convertToUIImage(ciImage: outputCIImage)
    }

    // MARK: - 多主体提取方法
    func extractMultipleSubjects(
        from image: UIImage,
        selectedIndices: Set<Int>,
        croppedToInstancesExtent: Bool = true
    ) -> UIImage? {
        guard let ciImage = CIImage(image: image) else { return nil }

        // 执行Vision请求
        let request = VNGenerateForegroundInstanceMaskRequest()
        let handler = VNImageRequestHandler(ciImage: ciImage)

        do {
            try handler.perform([request])
        } catch {
            return nil
        }

        guard let result = request.results?.first else { return nil }

        // 生成多主体掩码
        do {
            let mask = try result.generateScaledMaskForImage(
                forInstances: IndexSet(selectedIndices),
                from: handler
            )

            let maskImage = CIImage(cvPixelBuffer: mask)
            let backgroundImage = CIImage.empty()

            guard let outputImage = apply(
                mask: maskImage,
                toInputImage: ciImage,
                background: backgroundImage,
                croppedToInstancesExtent: croppedToInstancesExtent
            ) else {
                return nil
            }

            return convertToUIImage(ciImage: outputImage)
        } catch {
            return nil
        }
    }

    private func convertToUIImage(ciImage: CIImage) -> UIImage? {
        let context = CIContext(options: nil)
        guard let cgImage = context.createCGImage(ciImage, from: ciImage.extent) else {
            return nil
        }
        return UIImage(cgImage: cgImage)
    }

    // MARK: - 主题色提取
    /// 从图片中提取主题色（平均色）
    /// - Parameter image: 输入图片
    /// - Returns: 主题色的十六进制字符串，如 "4B7905"
    func extractThemeColor(from image: UIImage) -> String? {
        guard let ciImage = CIImage(image: image) else { return nil }

        // 使用 CIAreaAverage 滤镜计算平均色
        let filter = CIFilter.areaAverage()
        filter.inputImage = ciImage
        filter.extent = ciImage.extent

        guard let outputImage = filter.outputImage else { return nil }

        // 创建1x1像素的位图来获取平均色
        let context = CIContext(options: nil)
        var bitmap = [UInt8](repeating: 0, count: 4)

        context.render(outputImage,
                      toBitmap: &bitmap,
                      rowBytes: 4,
                      bounds: CGRect(x: 0, y: 0, width: 1, height: 1),
                      format: .RGBA8,
                      colorSpace: nil)

        // 转换为十六进制字符串
        let red = bitmap[0]
        let green = bitmap[1]
        let blue = bitmap[2]

        return String(format: "%02X%02X%02X", red, green, blue)
    }

    // MARK: - 图片保存
    func saveImageToAlbum(_ image: UIImage, completion: @escaping (Bool, Error?) -> Void) {
        UIImageWriteToSavedPhotosAlbum(image, nil, nil, nil)
        completion(true, nil)
    }
}

extension CVPixelBuffer {
    var ciImage: CIImage {
        return CIImage(cvPixelBuffer: self)
    }
}
