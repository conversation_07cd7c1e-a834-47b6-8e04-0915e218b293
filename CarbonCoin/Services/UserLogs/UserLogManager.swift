//
//  UserLogManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import Foundation
import SwiftUI

// MARK: - 用户日志管理服务协议

/// 用户日志管理服务协议，定义日志的CRUD操作
protocol UserLogManagerProtocol {
    /// 创建用户日志
    func createUserLog(_ request: CreateUserLogRequest) async throws -> UserLog

    /// 查询用户日志列表
    func fetchUserLogs(_ params: UserLogQueryParams) async throws -> UserLogListData

    /// 更新用户日志
    func updateUserLog(_ request: UpdateUserLogRequest) async throws -> UserLog

    /// 删除用户日志
    func deleteUserLog(logId: String, userId: String) async throws
}

// MARK: - 用户日志管理服务实现

/// 用户日志管理服务，负责处理用户日志的查询、更新、删除操作
@MainActor
class UserLogManager: ObservableObject, UserLogManagerProtocol {

    // MARK: - Published Properties

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    // MARK: - Private Properties

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateCoding()
    }

    // MARK: - Public Methods

    /// 创建用户日志
    func createUserLog(_ request: CreateUserLogRequest) async throws -> UserLog {
        guard let url = URL(string: "\(baseURL)user-logs") else {
            throw UserLogError.invalidURL
        }

        print("📝 创建用户日志: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let requestData = try jsonEncoder.encode(request)
            urlRequest.httpBody = requestData

            // 打印请求信息用于调试
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("📤 创建日志请求体: \(requestString)")
            }

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 创建日志响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 创建日志响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 201 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.userNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let operationResponse = try jsonDecoder.decode(UserLogOperationResponse.self, from: data)

            if operationResponse.success, let userLog = operationResponse.data {
                print("✅ 用户日志创建成功: \(userLog.id)")
                return userLog
            } else {
                throw UserLogError.responseError(operationResponse.error ?? "创建日志失败")
            }

        } catch let error as DecodingError {
            print("❌ 创建日志响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 创建日志网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    /// 查询用户日志列表
    func fetchUserLogs(_ params: UserLogQueryParams) async throws -> UserLogListData {
        var urlComponents = URLComponents(string: "\(baseURL)user-logs")

        // 构建查询参数
        var queryItems: [URLQueryItem] = [
            URLQueryItem(name: "userId", value: params.userId),
            URLQueryItem(name: "page", value: String(params.page ?? 1)),
            URLQueryItem(name: "limit", value: String(params.limit ?? 20))
        ]

        if let recordType = params.recordType {
            queryItems.append(URLQueryItem(name: "recordType", value: recordType.rawValue))
        }

        if let isPublic = params.isPublic {
            queryItems.append(URLQueryItem(name: "isPublic", value: String(isPublic)))
        }

        if let startDate = params.startDate {
            queryItems.append(URLQueryItem(name: "startDate", value: startDate))
        }

        if let endDate = params.endDate {
            queryItems.append(URLQueryItem(name: "endDate", value: endDate))
        }

        urlComponents?.queryItems = queryItems

        guard let url = urlComponents?.url else {
            throw UserLogError.invalidURL
        }

        print("📋 查询用户日志: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "GET"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 查询日志响应状态码: \(httpResponse.statusCode)")

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.userNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let listResponse = try jsonDecoder.decode(UserLogListResponse.self, from: data)

            if listResponse.success, let logListData = listResponse.data {
                print("✅ 用户日志查询成功，共 \(logListData.logs.count) 条记录")
                return logListData
            } else {
                throw UserLogError.responseError(listResponse.error ?? "查询日志失败")
            }

        } catch let error as DecodingError {
            print("❌ 查询日志响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 查询日志网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    /// 更新用户日志
    func updateUserLog(_ request: UpdateUserLogRequest) async throws -> UserLog {
        guard let url = URL(string: "\(baseURL)user-logs") else {
            throw UserLogError.invalidURL
        }

        print("✏️ 更新用户日志: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "PATCH"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let requestData = try jsonEncoder.encode(request)
            urlRequest.httpBody = requestData

            // 打印请求信息用于调试
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("📤 更新日志请求体: \(requestString)")
            }

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 更新日志响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 更新日志响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.logNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let operationResponse = try jsonDecoder.decode(UserLogOperationResponse.self, from: data)

            if operationResponse.success, let userLog = operationResponse.data {
                print("✅ 用户日志更新成功: \(userLog.id)")
                return userLog
            } else {
                throw UserLogError.responseError(operationResponse.error ?? "更新日志失败")
            }

        } catch let error as DecodingError {
            print("❌ 更新日志响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 更新日志网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    /// 删除用户日志
    func deleteUserLog(logId: String, userId: String) async throws {
        var urlComponents = URLComponents(string: "\(baseURL)user-logs")
        urlComponents?.queryItems = [
            URLQueryItem(name: "logId", value: logId),
            URLQueryItem(name: "userId", value: userId)
        ]

        guard let url = urlComponents?.url else {
            throw UserLogError.invalidURL
        }

        print("🗑️ 删除用户日志: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "DELETE"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 删除日志响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 删除日志响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.logNotFound
                } else if httpResponse.statusCode == 403 {
                    throw UserLogError.permissionDenied
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let deleteResponse = try jsonDecoder.decode(DeleteUserLogResponse.self, from: data)

            if deleteResponse.success {
                print("✅ 用户日志删除成功")
            } else {
                throw UserLogError.responseError(deleteResponse.error ?? "删除日志失败")
            }

        } catch let error as DecodingError {
            print("❌ 删除日志响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 删除日志网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    // MARK: - Private Methods

    /// 设置日期编码解码
    private func setupDateCoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
}
