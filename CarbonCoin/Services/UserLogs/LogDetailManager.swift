//
//  LogDetailManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import Foundation
import SwiftUI

// MARK: - 日志详情管理服务协议

/// 日志详情管理服务协议，定义点赞和评论操作
protocol LogDetailManagerProtocol {
    /// 创建点赞
    func createLike(_ request: CreateLikeRequest) async throws -> LogLike

    /// 取消点赞
    func deleteLike(logId: String, userId: String) async throws

    /// 创建评论
    func createComment(_ request: CreateCommentRequest) async throws -> LogComment

    /// 删除评论
    func deleteComment(commentId: String, userId: String) async throws
}

// MARK: - 日志详情管理服务实现

/// 日志详情管理服务，负责处理日志的点赞和评论功能
@MainActor
class LogDetailManager: ObservableObject, LogDetailManagerProtocol {

    // MARK: - Published Properties

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    // MARK: - Private Properties

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateCoding()
    }

    // MARK: - 点赞相关方法

    /// 创建点赞
    func createLike(_ request: CreateLikeRequest) async throws -> LogLike {
        guard let url = URL(string: "\(baseURL)user-logs/likes") else {
            throw UserLogError.invalidURL
        }

        print("👍 创建点赞: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let requestData = try jsonEncoder.encode(request)
            urlRequest.httpBody = requestData

            // 打印请求信息用于调试
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("📤 创建点赞请求体: \(requestString)")
            }

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 创建点赞响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 创建点赞响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 201 else {
                if httpResponse.statusCode == 400 {
                    throw UserLogError.alreadyLiked
                } else if httpResponse.statusCode == 404 {
                    throw UserLogError.logNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let likeResponse = try jsonDecoder.decode(LikeOperationResponse.self, from: data)

            if likeResponse.success, let like = likeResponse.data {
                print("✅ 点赞创建成功: \(like.id)")
                return like
            } else {
                throw UserLogError.responseError(likeResponse.error ?? "点赞失败")
            }

        } catch let error as DecodingError {
            print("❌ 创建点赞响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 创建点赞网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    /// 取消点赞
    func deleteLike(logId: String, userId: String) async throws {
        var urlComponents = URLComponents(string: "\(baseURL)user-logs/likes")
        urlComponents?.queryItems = [
            URLQueryItem(name: "logId", value: logId),
            URLQueryItem(name: "userId", value: userId)
        ]

        guard let url = urlComponents?.url else {
            throw UserLogError.invalidURL
        }

        print("👎 取消点赞: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "DELETE"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 取消点赞响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 取消点赞响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.likeNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let deleteResponse = try jsonDecoder.decode(DeleteLikeResponse.self, from: data)

            if deleteResponse.success {
                print("✅ 取消点赞成功")
            } else {
                throw UserLogError.responseError(deleteResponse.error ?? "取消点赞失败")
            }

        } catch let error as DecodingError {
            print("❌ 取消点赞响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 取消点赞网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    // MARK: - 评论相关方法

    /// 创建评论
    func createComment(_ request: CreateCommentRequest) async throws -> LogComment {
        // 验证评论内容
        guard !request.content.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            throw UserLogError.emptyContent
        }

        guard request.content.count <= 500 else {
            throw UserLogError.commentTooLong
        }

        guard let url = URL(string: "\(baseURL)user-logs/comments") else {
            throw UserLogError.invalidURL
        }

        print("💬 创建评论: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "POST"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let requestData = try jsonEncoder.encode(request)
            urlRequest.httpBody = requestData

            // 打印请求信息用于调试
            if let requestString = String(data: requestData, encoding: .utf8) {
                print("📤 创建评论请求体: \(requestString)")
            }

            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 创建评论响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 创建评论响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 201 else {
                if httpResponse.statusCode == 400 {
                    throw UserLogError.invalidParameters
                } else if httpResponse.statusCode == 404 {
                    throw UserLogError.logNotFound
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let commentResponse = try jsonDecoder.decode(CommentOperationResponse.self, from: data)

            if commentResponse.success, let comment = commentResponse.data {
                print("✅ 评论创建成功: \(comment.id)")
                return comment
            } else {
                throw UserLogError.responseError(commentResponse.error ?? "评论失败")
            }

        } catch let error as DecodingError {
            print("❌ 创建评论响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 创建评论网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    /// 删除评论
    func deleteComment(commentId: String, userId: String) async throws {
        var urlComponents = URLComponents(string: "\(baseURL)user-logs/comments")
        urlComponents?.queryItems = [
            URLQueryItem(name: "commentId", value: commentId),
            URLQueryItem(name: "userId", value: userId)
        ]

        guard let url = urlComponents?.url else {
            throw UserLogError.invalidURL
        }

        print("🗑️ 删除评论: \(url.absoluteString)")

        var urlRequest = URLRequest(url: url)
        urlRequest.httpMethod = "DELETE"
        urlRequest.setValue("application/json", forHTTPHeaderField: "Content-Type")

        do {
            let (data, response) = try await URLSession.shared.data(for: urlRequest)

            guard let httpResponse = response as? HTTPURLResponse else {
                throw UserLogError.networkError(URLError(.badServerResponse))
            }

            print("📥 删除评论响应状态码: \(httpResponse.statusCode)")

            // 打印响应数据用于调试
            if let responseString = String(data: data, encoding: .utf8) {
                print("📥 删除评论响应内容: \(responseString)")
            }

            guard httpResponse.statusCode == 200 else {
                if httpResponse.statusCode == 404 {
                    throw UserLogError.commentNotFound
                } else if httpResponse.statusCode == 403 {
                    throw UserLogError.permissionDenied
                }
                throw UserLogError.serverError(httpResponse.statusCode)
            }

            let deleteResponse = try jsonDecoder.decode(DeleteCommentResponse.self, from: data)

            if deleteResponse.success {
                print("✅ 评论删除成功")
            } else {
                throw UserLogError.responseError(deleteResponse.error ?? "删除评论失败")
            }

        } catch let error as DecodingError {
            print("❌ 删除评论响应解析失败: \(error)")
            throw UserLogError.decodingError
        } catch let error as UserLogError {
            throw error
        } catch {
            print("❌ 删除评论网络请求失败: \(error)")
            throw UserLogError.networkError(error)
        }
    }

    // MARK: - Private Methods

    /// 设置日期编码解码
    private func setupDateCoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
}
