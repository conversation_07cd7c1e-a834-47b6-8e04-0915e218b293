//
//  LocationUpdater.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import Foundation
import CoreLocation

// MARK: - 位置更新服务协议

/// 位置更新服务协议，定义位置查询和更新的接口
protocol LocationUpdaterProtocol {
    /// 查询用户位置
    /// - Parameter userId: 用户ID
    /// - Returns: 用户位置信息
    func queryUserLocation(userId: String) async throws -> UserLocationInfo

    /// 更新用户位置
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - location: 位置信息
    /// - Returns: 更新是否成功
    func updateUserLocation(userId: String, location: CLLocation) async throws -> Bool

    /// 更新用户位置
    /// - Parameter userLocationInfo: 用户位置信息
    /// - Returns: 更新是否成功
    func updateUserLocation(userLocationInfo: UserLocationInfo) async throws -> Bool
}

// MARK: - 位置更新服务

/// 位置更新服务，负责处理用户位置的查询和更新
@MainActor
class LocationUpdater: ObservableObject, LocationUpdaterProtocol {

    // MARK: - Published Properties

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 最后更新时间
    @Published var lastUpdateTime: Date?

    // MARK: - Private Properties

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateDecoding()
    }

    // MARK: - Public Methods

    /// 查询用户位置
    /// - Parameter userId: 用户ID
    /// - Returns: 用户位置信息
    func queryUserLocation(userId: String) async throws -> UserLocationInfo {
        guard !userId.isEmpty else {
            throw LocationError.missingUserId
        }

        isLoading = true
        errorMessage = nil

        do {
            let locationInfo = try await performQueryLocationRequest(userId: userId)
            print("✅ 成功查询用户位置: \(userId)")
            return locationInfo
        } catch {
            let locationError = mapError(error)
            errorMessage = locationError.localizedDescription
            print("❌ 查询用户位置失败: \(locationError.localizedDescription)")
            throw locationError
        }

        isLoading = false
    }

    /// 更新用户位置
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - location: 位置信息
    /// - Returns: 更新是否成功
    func updateUserLocation(userId: String, location: CLLocation) async throws -> Bool {
        guard !userId.isEmpty else {
            throw LocationError.missingUserId
        }

        // 检查位置精度
        guard location.horizontalAccuracy < LocationUpdateConfig.accuracyThreshold else {
            throw LocationError.invalidLocation
        }

        isLoading = true
        errorMessage = nil

        do {
            let success = try await performUpdateLocationRequest(userId: userId, location: location)
            if success {
                lastUpdateTime = Date()
                print("✅ 成功更新用户位置: \(userId)")
            }
            return success
        } catch {
            let locationError = mapError(error)
            errorMessage = locationError.localizedDescription
            print("❌ 更新用户位置失败: \(locationError.localizedDescription)")
            throw locationError
        }

        isLoading = false
    }

    /// 更新用户位置
    /// - Parameter userLocationInfo: 用户位置信息
    /// - Returns: 更新是否成功
    func updateUserLocation(userLocationInfo: UserLocationInfo) async throws -> Bool {
        return try await updateUserLocation(userId: userLocationInfo.userId, location: userLocationInfo.clLocation)
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Private Methods

    /// 设置日期解码
    private func setupDateDecoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 执行查询位置请求
    private func performQueryLocationRequest(userId: String) async throws -> UserLocationInfo {
        guard let url = URL(string: baseURL + "location?userId=\(userId)") else {
            throw LocationError.invalidResponse
        }

        print("📍 查询用户位置: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = LocationUpdateConfig.requestTimeout

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw LocationError.networkError
        }

        print("📥 查询位置响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 查询位置响应内容: \(responseString)")
        }

        // 处理不同的HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw LocationError.missingUserId
        case 404:
            throw LocationError.userNotFound
        case 500:
            throw LocationError.serverError
        default:
            throw LocationError.serverError
        }

        let locationResponse = try jsonDecoder.decode(LocationQueryResponse.self, from: data)

        if !locationResponse.success {
            throw LocationError.unknown(locationResponse.error ?? "查询位置失败")
        }

        guard let locationData = locationResponse.data else {
            throw LocationError.invalidResponse
        }

        return locationData.toUserLocationInfo()
    }

    /// 执行更新位置请求
    private func performUpdateLocationRequest(userId: String, location: CLLocation) async throws -> Bool {
        guard let url = URL(string: baseURL + "location") else {
            throw LocationError.invalidResponse
        }

        print("📍 更新用户位置: \(url.absoluteString)")
        print("📍 位置坐标: \(location.coordinate.latitude), \(location.coordinate.longitude)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.timeoutInterval = LocationUpdateConfig.requestTimeout

        let requestBody = UpdateLocationRequest(userId: userId, location: location)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw LocationError.networkError
        }

        print("📥 更新位置响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 更新位置响应内容: \(responseString)")
        }

        // 处理不同的HTTP状态码
        switch httpResponse.statusCode {
        case 200:
            break
        case 400:
            throw LocationError.missingRequiredFields
        case 500:
            throw LocationError.serverError
        default:
            throw LocationError.serverError
        }

        let updateResponse = try jsonDecoder.decode(LocationUpdateResponse.self, from: data)

        return updateResponse.success
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> LocationError {
        if let locationError = error as? LocationError {
            return locationError
        }

        if error is URLError {
            return .networkError
        }

        return .unknown(error.localizedDescription)
    }
}
