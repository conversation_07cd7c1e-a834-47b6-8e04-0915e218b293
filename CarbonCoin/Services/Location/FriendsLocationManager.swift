//
//  FriendsLocationManager.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/29.
//

import Foundation
import Combine

// MARK: - 好友位置管理器协议

/// 好友位置管理器协议，定义获取好友位置信息的接口
protocol FriendsLocationManagerProtocol {
    /// 批量获取好友位置信息
    /// - Parameter friendIds: 好友用户ID数组
    /// - Returns: 好友位置信息数组
    func fetchFriendsLocation(friendIds: [String]) async throws -> [FriendLocationInfo]
}

// MARK: - 好友位置管理器

/// 好友位置管理器，专门负责处理好友位置相关的网络请求
/// 注意：此类已解耦，不再直接依赖 FriendViewModel，只负责纯粹的位置数据获取
@MainActor
class FriendsLocationManager: ObservableObject, FriendsLocationManagerProtocol {

    // MARK: - Published Properties

    /// 好友位置信息列表
    @Published var friendsLocationInfo: [FriendLocationInfo] = []

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    // MARK: - Private Properties

    private let baseURL = AuthConfig.baseURL
    private let jsonEncoder = JSONEncoder()
    private let jsonDecoder = JSONDecoder()

    // MARK: - Initialization

    init() {
        setupDateDecoding()
    }

    // MARK: - Public Methods

    /// 批量获取好友位置信息
    /// - Parameter friendIds: 好友用户ID数组
    /// - Returns: 好友位置信息数组
    func fetchFriendsLocation(friendIds: [String]) async throws -> [FriendLocationInfo] {
        guard !friendIds.isEmpty else {
            return []
        }

        isLoading = true
        errorMessage = nil

        do {
            let locationInfo = try await performFriendsLocationRequest(friendIds: friendIds)
            friendsLocationInfo = locationInfo
            print("✅ 成功获取 \(locationInfo.count) 个好友的位置信息")
            isLoading = false
            return locationInfo
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            print("❌ 获取好友位置信息失败: \(friendError.localizedDescription)")
            isLoading = false
            throw friendError
        }
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    // MARK: - Private Methods

    /// 设置日期解码
    private func setupDateDecoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)
        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 执行好友位置请求
    private func performFriendsLocationRequest(friendIds: [String]) async throws -> [FriendLocationInfo] {
        guard let url = URL(string: baseURL + "friendsLocation") else {
            throw FriendError.invalidResponse
        }

        print("📍 批量获取好友位置: \(url.absoluteString)")
        print("📍 查询好友ID: \(friendIds)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = FriendsLocationRequest(friendIds: friendIds)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }

        print("📥 好友位置响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 好友位置响应内容: \(responseString)")
        }

        guard (200...299).contains(httpResponse.statusCode) else {
            throw FriendError.serverError
        }

        let locationResponse = try jsonDecoder.decode(FriendsLocationResponse.self, from: data)

        if !locationResponse.success {
            throw FriendError.unknown(locationResponse.error ?? "获取好友位置信息失败")
        }

        guard let locationData = locationResponse.data else {
            throw FriendError.invalidResponse
        }

        return locationData.friends
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> FriendError {
        if let friendError = error as? FriendError {
            return friendError
        }

        if error is URLError {
            return .networkError
        }

        return .unknown(error.localizedDescription)
    }
}
