//
//  FriendshipListView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import SwiftUI

// MARK: - 好友列表视图

/// 显示当前用户的好友列表
struct FriendshipListView: View {

    // MARK: 属性

    @StateObject private var friendViewModel = FriendViewModel()
    @StateObject private var authManager = AuthManager()
    @State private var searchText = ""
    @State private var selectedSegment = 0
    @State private var showingAddFriend = false

    // MARK: 计算属性

    /// 当前用户ID
    private var currentUserId: String {
        return authManager.currentUserId
    }

    /// 过滤后的好友列表
    private var filteredFriends: [Friendship] {
        if searchText.isEmpty {
            return friendViewModel.friends
        } else {
            return friendViewModel.friends.filter { friendship in
                friendship.friend.nickname.localizedCaseInsensitiveContains(searchText) ||
                friendship.friend.userId.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    /// 过滤后的待处理请求
    private var filteredPendingRequests: [Friendship] {
        if searchText.isEmpty {
            return friendViewModel.pendingRequests
        } else {
            return friendViewModel.pendingRequests.filter { friendship in
                friendship.friend.nickname.localizedCaseInsensitiveContains(searchText) ||
                friendship.friend.userId.localizedCaseInsensitiveContains(searchText)
            }
        }
    }

    // MARK: - Body

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: 0) {
                    // 搜索栏
                    searchBarView

                    // 分段控制器
                    segmentedControlView

                    // 内容区域
                    contentView
                }
            }
            .navigationTitle("好友")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingAddFriend = true
                    }) {
                        Image(systemName: "person.badge.plus")
                    }
                }
            }
            .sheet(isPresented: $showingAddFriend) {
                FriendAddView()
            }
            .onAppear {
                Task {
                    await friendViewModel.fetchFriendList(for: currentUserId)
                }
            }
            .refreshable {
                await friendViewModel.fetchFriendList(for: currentUserId)
            }
            .alert("错误", isPresented: .constant(friendViewModel.errorMessage != nil)) {
                Button("确定") {
                    friendViewModel.clearMessages()
                }
            } message: {
                if let error = friendViewModel.errorMessage {
                    Text(error)
                }
            }
            .alert("成功", isPresented: .constant(friendViewModel.successMessage != nil)) {
                Button("确定") {
                    friendViewModel.clearMessages()
                }
            } message: {
                if let success = friendViewModel.successMessage {
                    Text(success)
                }
            }
        }
    }

    // MARK: - Private Views

    /// 搜索栏视图
    private var searchBarView: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.textSecondary)

            TextField("搜索好友", text: $searchText)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .textFieldStyle(PlainTextFieldStyle())

            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.glassBackground)
        )
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.top, Theme.Spacing.sm)
    }

    /// 分段控制器视图
    private var segmentedControlView: some View {
        Picker("好友类型", selection: $selectedSegment) {
            Text("好友 (\(friendViewModel.friends.count))").tag(0)
            Text("请求 (\(friendViewModel.pendingRequests.count))").tag(1)
        }
        .pickerStyle(SegmentedPickerStyle())
        .padding(.horizontal, Theme.Spacing.md)
        .padding(.top, Theme.Spacing.md)
    }

    /// 内容视图
    private var contentView: some View {
        Group {
            if friendViewModel.isLoading {
                loadingView
            } else {
                switch selectedSegment {
                case 0:
                    friendsListView
                case 1:
                    pendingRequestsView
                default:
                    friendsListView
                }
            }
        }
    }

    /// 加载视图
    private var loadingView: some View {
        VStack {
            Spacer()
            ProgressView("加载中...")
                .foregroundColor(.textPrimary)
            Spacer()
        }
    }

    /// 好友列表视图
    private var friendsListView: some View {
        Group {
            if filteredFriends.isEmpty {
                emptyFriendsView
            } else {
                ScrollView {
                    LazyVStack(spacing: Theme.Spacing.md) {
                        ForEach(filteredFriends) { friendship in
                            FriendItem(
                                userInfo: friendship.friend,
                                relationshipState: .friend,
                                onButtonTap: {
                                    // 查看好友资料的逻辑
                                    print("查看好友资料: \(friendship.friend.userId)")
                                }
                            )
                        }
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
        }
    }

    /// 待处理请求视图
    private var pendingRequestsView: some View {
        Group {
            if filteredPendingRequests.isEmpty {
                emptyRequestsView
            } else {
                ScrollView {
                    LazyVStack(spacing: Theme.Spacing.md) {
                        ForEach(filteredPendingRequests) { friendship in
                            FriendItem(
                                userInfo: friendship.friend,
                                relationshipState: .pendingReceived,
                                onButtonTap: {
                                    // 这里不需要处理，因为待确认状态使用专门的接受/拒绝按钮
                                },
                                onAcceptTap: {
                                    Task {
                                        await friendViewModel.handleFriendRequest(
                                            userId: currentUserId,
                                            friendId: friendship.friend.userId,
                                            action: .accept
                                        )
                                    }
                                },
                                onRejectTap: {
                                    Task {
                                        await friendViewModel.handleFriendRequest(
                                            userId: currentUserId,
                                            friendId: friendship.friend.userId,
                                            action: .reject
                                        )
                                    }
                                }
                            )
                        }
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
        }
    }

    /// 空好友列表视图
    private var emptyFriendsView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            Spacer()

            Image(systemName: "person.2")
                .font(.system(size: 60))
                .foregroundColor(.textSecondary)

            Text("还没有好友")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Text("点击右上角的 + 按钮添加好友")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)

            Button(action: {
                showingAddFriend = true
            }) {
                Text("添加好友")
                    .font(.bodyBrand)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        Capsule()
                            .fill(Color(hex: "4B7905"))
                    )
            }

            Spacer()
        }
        .padding(.horizontal, Theme.Spacing.lg)
    }

    /// 空请求列表视图
    private var emptyRequestsView: some View {
        VStack(spacing: Theme.Spacing.lg) {
            Spacer()

            Image(systemName: "person.badge.clock")
                .font(.system(size: 60))
                .foregroundColor(.textSecondary)

            Text("没有待处理的好友请求")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            Text("当有人向您发送好友请求时，会在这里显示")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)

            Spacer()
        }
        .padding(.horizontal, Theme.Spacing.lg)
    }
}

// MARK: - 预览

#Preview {
    FriendshipListView()
        .stableBackground()
}
