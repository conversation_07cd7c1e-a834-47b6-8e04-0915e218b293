//
//  ItemCardLibrary.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import SwiftUI

struct ItemCardLibrary: View {
    @EnvironmentObject var cardStore: CardStore
    @StateObject private var userItemCardViewModel = UserItemCardViewModel()
    @State private var searchText = ""
    @State private var selectedFilter: CardFilterType = .all
    @AppStorage("currentUserId") private var currentUserId: String = ""

    // 过滤后的卡片
    private var filteredUserItemCards: [UserItemCard] {
        var cards = userItemCardViewModel.userItemCards

        // 根据筛选类型过滤
        switch selectedFilter {
        case .all:
            break // 显示所有卡片
        case .created:
            cards = cards.filter { $0.isAuthor }
        case .received:
            cards = cards.filter { !$0.isAuthor }
        }

        // 根据搜索文本过滤
        if !searchText.isEmpty {
            cards = cards.filter { userItemCard in
                userItemCard.card?.title.localizedCaseInsensitiveContains(searchText) ?? false
            }
        }

        // 按获取时间排序
        return cards.sorted { $0.acquiredAt > $1.acquiredAt }
    }

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: 0) {
                    // 搜索和筛选区域
                    searchAndFilterSection
                        .padding(.horizontal, Theme.Spacing.md)
                        .padding(.top, Theme.Spacing.sm)

                    if filteredUserItemCards.isEmpty {
                        // 空状态视图
                        EmptyCardListView(filterType: selectedFilter)
                    } else {
                        // 卡片列表
                        ScrollView {
                            LazyVStack(spacing: Theme.Spacing.md) {
                                ForEach(filteredUserItemCards) { userItemCard in
                                    ItemCardView(userItemCard: userItemCard, displayStyle: .compact)
                                }
                            }
                            .padding(.horizontal, Theme.Spacing.md)
                            .padding(.bottom, Theme.Spacing.tab)
                            .padding(.top, Theme.Spacing.lg)
                        }
                        .scrollContentBackground(.hidden)
                    }
                }
            }
            .navigationTitle("卡片库")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .onAppear {
                loadUserItemCards()
            }
        }
    }

    // MARK: - 搜索和筛选区域
    private var searchAndFilterSection: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack(spacing: Theme.Spacing.md) {
                // 搜索框
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.textSecondary)

                    TextField("搜索卡片标题...", text: $searchText)
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                }
                .padding(.horizontal, Theme.Spacing.md)
                .padding(.vertical, Theme.Spacing.sm)
                .background(Color.cardBackground.opacity(0.3))
                .cornerRadius(Theme.CornerRadius.md)
                .glassCard()

                // 筛选选择器
                Picker("筛选", selection: $selectedFilter) {
                    ForEach(CardFilterType.allCases, id: \.self) { filterType in
                        Text(filterType.displayName)
                            .tag(filterType)
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .padding(.horizontal, Theme.Spacing.sm)
                .padding(.vertical, Theme.Spacing.xs)
                .background(Color.cardBackground.opacity(0.3))
                .cornerRadius(Theme.CornerRadius.md)
                .glassCard()
            }
        }
    }



    // MARK: - 数据加载方法
    private func loadUserItemCards() {
        Task {
            await userItemCardViewModel.fetchUserItemCards(for: currentUserId)
        }
    }
}

#Preview {
    let cardStore = CardStore()
    ItemCardLibrary()
        .environmentObject(cardStore)
}
