//
//  FriendAddView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import SwiftUI

// MARK: - 添加好友视图

/// 用户搜索和添加好友的专用页面
struct FriendAddView: View {

    // MARK: - Properties

    @StateObject private var friendViewModel = FriendViewModel()
    @StateObject private var authManager = AuthManager()
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var isSearching = false

    // MARK: - Computed Properties

    /// 当前用户ID
    private var currentUserId: String {
        return authManager.currentUserId
    }

    /// 搜索结果的关系状态
    private var searchResultRelationshipState: FriendRelationshipState {
        guard let searchedUser = friendViewModel.searchedUser else {
            return .notFriend
        }
        return friendViewModel.getRelationshipState(with: searchedUser.userId)
    }

    // MARK: - Body

    var body: some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                VStack(spacing: Theme.Spacing.lg) {
                    // 搜索区域
                    searchSectionView

                    // 搜索结果区域
                    searchResultView

                    Spacer()
                }
                .padding(.horizontal, Theme.Spacing.md)
                .padding(.top, Theme.Spacing.md)
            }
            .navigationTitle("添加好友")
            .navigationBarTitleDisplayMode(.inline)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .alert("错误", isPresented: .constant(friendViewModel.errorMessage != nil)) {
                Button("确定") {
                    friendViewModel.clearMessages()
                }
            } message: {
                if let error = friendViewModel.errorMessage {
                    Text(error)
                }
            }
            .alert("成功", isPresented: .constant(friendViewModel.successMessage != nil)) {
                Button("确定") {
                    friendViewModel.clearMessages()
                    // 成功后刷新好友列表并关闭页面
                    Task {
                        await friendViewModel.fetchFriendList(for: currentUserId)
                    }
                }
            } message: {
                if let success = friendViewModel.successMessage {
                    Text(success)
                }
            }
        }
    }

    // MARK: - Private Views

    /// 搜索区域视图
    private var searchSectionView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            // 标题
            Text("搜索用户")
                .font(.title3Brand)
                .foregroundColor(.textPrimary)

            // 搜索输入框
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.textSecondary)

                TextField("输入用户ID", text: $searchText)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .textFieldStyle(PlainTextFieldStyle())
                    .autocapitalization(.none)
                    .disableAutocorrection(true)
                    .onSubmit {
                        performSearch()
                    }

                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                        friendViewModel.clearSearchResults()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.textSecondary)
                    }
                }
            }
            .padding(Theme.Spacing.md)
            .background(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(Color.glassBackground)
            )

            // 搜索按钮
            Button(action: {
                performSearch()
            }) {
                HStack {
                    if isSearching {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.8)
                    }
                    Text(isSearching ? "搜索中..." : "搜索")
                        .font(.bodyBrand)
                        .foregroundColor(.white)
                }
                .frame(maxWidth: .infinity)
                .frame(height: 44)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .fill(searchText.isEmpty ? Color.gray : Color(hex: "4B7905"))
                )
            }
            .disabled(searchText.isEmpty || isSearching)

            // 搜索提示
            Text("输入完整的用户ID进行搜索")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }

    /// 搜索结果视图
    private var searchResultView: some View {
        Group {
            if let searchedUser = friendViewModel.searchedUser {
                // 显示搜索到的用户
                VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                    Text("搜索结果")
                        .font(.title3Brand)
                        .foregroundColor(.textPrimary)

                    FriendItem(
                        userInfo: searchedUser,
                        relationshipState: searchResultRelationshipState,
                        onButtonTap: {
                            handleFriendAction(for: searchedUser)
                        },
                        onAcceptTap: {
                            Task {
                                await friendViewModel.handleFriendRequest(
                                    userId: currentUserId,
                                    friendId: searchedUser.userId,
                                    action: .accept
                                )
                            }
                        },
                        onRejectTap: {
                            Task {
                                await friendViewModel.handleFriendRequest(
                                    userId: currentUserId,
                                    friendId: searchedUser.userId,
                                    action: .reject
                                )
                            }
                        }
                    )
                }
                .padding(Theme.Spacing.lg)
                .glassCard()
            } else if friendViewModel.errorMessage == nil && !searchText.isEmpty && !isSearching {
                // 显示搜索提示
                emptySearchResultView
            }
        }
    }

    /// 空搜索结果视图
    private var emptySearchResultView: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "person.fill.questionmark")
                .font(.system(size: 40))
                .foregroundColor(.textSecondary)

            Text("未找到用户")
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)

            Text("请检查用户ID是否正确")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }

    // MARK: - Private Methods

    /// 执行搜索
    private func performSearch() {
        guard !searchText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            return
        }

        isSearching = true

        Task {
            await friendViewModel.searchUser(userId: searchText.trimmingCharacters(in: .whitespacesAndNewlines))
            isSearching = false
        }
    }

    /// 处理好友操作
    private func handleFriendAction(for user: UserInfo) {
        let relationshipState = friendViewModel.getRelationshipState(with: user.userId)

        switch relationshipState {
        case .notFriend, .rejected:
            // 发送好友请求
            Task {
                await friendViewModel.sendFriendRequest(from: currentUserId, to: user.userId)
            }
        case .friend:
            // 查看好友资料（暂时只打印）
            print("查看好友资料: \(user.userId)")
        case .pendingSent:
            // 已发送请求，不需要操作
            break
        case .pendingReceived:
            // 这种情况由FriendItem的专门按钮处理
            break
        }
    }
}

// MARK: - 预览

#Preview {
    FriendAddView()
        .stableBackground()
}
