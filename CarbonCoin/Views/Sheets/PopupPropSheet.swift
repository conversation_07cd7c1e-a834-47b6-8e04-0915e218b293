//
//  PopupPropSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

// MARK: - 道具消息详细弹窗组件

/// 道具消息详细弹窗，用于在 MapView 中显示未读道具消息
/// 包含发送时间、头像、道具动画、道具名称和备注信息
struct PopupPropSheet: View {

    // MARK: - Properties

    let interaction: PropInteraction
    @Environment(\.dismiss) private var dismiss

    // MARK: - State

    @State private var showAnimation = false

    // MARK: - Body

    var body: some View {
        VStack(spacing: Theme.Spacing.lg) {
            // 顶部：发送时间
            timeHeaderView

            // 中间主体内容
            mainContentView

            // 底部：备注信息
            remarkView

            // 关闭按钮
            closeButton
        }
        .padding(Theme.Spacing.lg)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.2), radius: 12, x: 0, y: 6)
        .frame(maxWidth: UIScreen.main.bounds.width * 0.85)
        .onAppear {
            withAnimation(.easeInOut(duration: 0.5).delay(0.2)) {
                showAnimation = true
            }
        }
    }

    // MARK: 时间和主体显示视图

    /// 顶部时间显示
    private var timeHeaderView: some View {
        HStack(spacing: Theme.Spacing.xs) {
            Text(interaction.sender.nickname)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.semibold)
            Text("在")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)

            Text(formatInteractionTime(interaction.interactionTime))
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)
            
            Text("向您投送了一个道具")
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
        }
    }

    /// 主体内容区域
    private var mainContentView: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 道具动画和名称
            propAnimationView
        }
    }

    /// 发送者信息视图
    private var senderInfoView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 头像
            AsyncImage(url: URL(string: interaction.sender.avatarURL ?? "")) { image in
                image
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            } placeholder: {
                Image(systemName: "person.circle.fill")
                    .font(.title)
                    .foregroundColor(.textSecondary)
            }
            .frame(width: 50, height: 50)
            .clipShape(Circle())
            .overlay(
                Circle()
                    .stroke(Color.brandGreen.opacity(0.5), lineWidth: 2)
            )

            // 发送者名字
            VStack(alignment: .leading, spacing: 2) {
                Text(interaction.sender.nickname)
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)
                    .fontWeight(.semibold)

                Text("给您发送了道具")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }

            Spacer()
        }
    }

    // MARK:  道具动画和名称视图
    private var propAnimationView: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 道具动画
            if let propInfo = interaction.propInfo {
                LottieHelperView(
                    fileName: "\(propInfo.propTitle).json",
                    contentMode: .scaleAspectFit,
                    playLoopMode: .loop,
                    animationProgress: 1
                )
                .frame(maxWidth: .infinity)
                .scaleEffect(showAnimation ? 1.0 : 0.8)
                .opacity(showAnimation ? 1.0 : 0.0)
                .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showAnimation)

                // 道具名称
                Text(propInfo.propTitle)
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)
                    .fontWeight(.bold)
                    .scaleEffect(showAnimation ? 1.0 : 0.9)
                    .opacity(showAnimation ? 1.0 : 0.0)
                    .animation(.easeInOut(duration: 0.5).delay(0.3), value: showAnimation)
            } else {
                // 备用显示（如果道具信息不存在）
                VStack(spacing: Theme.Spacing.sm) {
                    Image(systemName: "gift.fill")
                        .font(.system(size: 60))
                        .foregroundColor(.brandGreen)
                        .scaleEffect(showAnimation ? 1.0 : 0.8)
                        .opacity(showAnimation ? 1.0 : 0.0)
                        .animation(.spring(response: 0.6, dampingFraction: 0.8), value: showAnimation)

                    Text("神秘道具")
                        .font(.title2Brand)
                        .foregroundColor(.textPrimary)
                        .fontWeight(.bold)
                }
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.brandGreen.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.brandGreen.opacity(0.2), lineWidth: 1)
                )
        )
    }

    /// 备注信息视图
    private var remarkView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            Text("备注：")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .fontWeight(.medium)

            Text(interaction.displayRemark)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .multilineTextAlignment(.leading)
                .lineLimit(nil)
                .padding(Theme.Spacing.sm)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.cardBackground.opacity(0.5))
                )
        }
    }

    /// 关闭按钮
    private var closeButton: some View {
        Button(action: {
            dismiss()
        }) {
            Text("已阅")
                .font(.bodyBrand)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .frame(maxWidth: UIScreen.main.bounds.size.width * 0.5)
                .padding(.vertical, Theme.Spacing.sm)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(Color.brandGreen)
                )
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Helper Methods

    /// 格式化交互时间
    private func formatInteractionTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)

        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            formatter.dateFormat = "MM-dd HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Preview

#Preview {
    PopupPropSheet(
        interaction: PropInteraction(
            id: "preview-id",
            senderUserId: "sender123",
            receiverUserId: "receiver456",
            propId: 1,
            remark: "您的好友正在火速赶来的路上...",
            interactionTime: Date().addingTimeInterval(-300), // 5分钟前
            receivedTime: nil,
            isRead: false,
            sender: PropInteractionUser(
                userId: "sender123",
                nickname: "小明",
                avatarURL: nil
            ),
            receiver: PropInteractionUser(
                userId: "receiver456",
                nickname: "小红",
                avatarURL: nil
            )
        )
    )
    .padding()
    .background(Color.black.opacity(0.3))
    .preferredColorScheme(.dark)
}
