//
//  PetAcquisitionSheet.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/25.
//

import SwiftUI

struct PetAcquisitionSheet: View {
    let displayModel: PetDisplayModel
    let currentAmount: Int
    let onPurchase: () -> Void

    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            VStack(spacing: Theme.Spacing.lg) {
                // 宠物灰色图像
                petImageSection

                // 宠物信息
                petInfoSection

                // 获得条件
                acquisitionConditionSection

                Spacer()

                // 获得宠物按钮
                acquisitionButton
            }
            .padding(Theme.Spacing.lg)
            .navigationTitle("获得宠物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }

    // MARK: 宠物图像区域
    private var petImageSection: some View {
        ZStack {
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(LinearGradient.petItemBgColor)
                .frame(width: 200, height: 200)
                .shadow(color: .black.opacity(0.3), radius: 10, y: 5)

            if let petImage = UIImage(named: displayModel.template.imageName) {
                Image(uiImage: petImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(width: 180, height: 180)
                    .colorMultiply(.black.opacity(0.5)) // 灰色效果
            }
        }
        .glassCard()
    }

    // MARK: 宠物信息区域
    private var petInfoSection: some View {
        VStack(spacing: Theme.Spacing.md) {
            Text(displayModel.template.name)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)

            HStack(spacing: 2) {
                ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                    Image("star")
                        .foregroundColor(.yellow)
                        .font(.system(size: 16))
                }
            }

            Text(displayModel.template.introduction)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .padding()
        .glassCard()
    }

    // MARK: 获得条件区域
    private var acquisitionConditionSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            Text("获得条件")
                .font(.headline)
                .foregroundColor(.textPrimary)

            switch displayModel.template.acquisition {
            case .purchase(let amount):
                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.brandGreen)
                    Text("需要碳币: \(amount)")
                        .font(.bodyBrand)
                    Spacer()
                    Text("当前: \(currentAmount)")
                        .font(.bodyBrand)
                        .foregroundColor(currentAmount >= amount ? .success : .error)
                }

            case .loginDays(let days):
                HStack {
                    Image(systemName: "calendar.badge.clock")
                        .foregroundColor(.info)
                    Text("需要连续登录: \(days) 天")
                        .font(.bodyBrand)
                    Spacer()
                    // 显示当前登录天数
                    LoginDaysStatusView(requiredDays: days)
                }

            case .task(let taskId):
                HStack {
                    Image(systemName: "checkmark.circle")
                        .foregroundColor(.brandColor)
                    Text("完成指定任务: \(taskId)")
                        .font(.bodyBrand)
                    Spacer()
                    // 任务系统暂未实现，显示待开发状态
                    Text("待开发")
                        .font(.captionBrand)
                        .foregroundColor(.orange)
                }
            }
        }
        .padding()
        .background(Color.cardBackground.opacity(0.3))
        .cornerRadius(Theme.CornerRadius.md)
    }

    // MARK: 获得宠物按钮
    private var acquisitionButton: some View {
        Button(action: {
            switch displayModel.template.acquisition {
            case .purchase(_):
                onPurchase()
            case .loginDays(_):
                // 通过登录天数获得宠物
                onPurchase() // 复用购买逻辑，但不扣除金币
            case .task(_):
                // 任务系统暂未实现，暂不处理
                break
            }
        }) {
            HStack {
                Image(systemName: "heart.fill")
                Text("获得宠物")
                    .font(.bodyBrand)
                    .fontWeight(.semibold)
            }
            .foregroundColor(.white)
            .frame(maxWidth: .infinity)
            .padding()
            .background(
                canAcquire ? Color.brandGreen : Color.gray
            )
            .cornerRadius(Theme.CornerRadius.md)
        }
        .disabled(!canAcquire)
    }

    // 是否可以获得
    @MainActor
    private var canAcquire: Bool {
        switch displayModel.template.acquisition {
        case .purchase(let amount):
            return currentAmount >= amount
        case .loginDays(let days):
            // 检查登录天数是否满足条件
            let appSettings = AppSettings()
            return appSettings.settings.appUsageDays >= days
        case .task(_):
            // 任务系统暂未实现，暂时返回false
            return false
        }
    }
}

// MARK: - 登录天数状态视图
struct LoginDaysStatusView: View {
    let requiredDays: Int
    @StateObject private var appSettings = AppSettings()

    var body: some View {
        let currentDays = appSettings.settings.appUsageDays
        Text("当前: \(currentDays) 天")
            .font(.captionBrand)
            .foregroundColor(currentDays >= requiredDays ? .success : .textSecondary)
    }
}
