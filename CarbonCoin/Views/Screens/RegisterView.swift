//
//  RegisterView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import SwiftUI
import TipKit

struct RegisterView: View {
    @StateObject private var authViewModel = AuthViewModel()
    @StateObject private var authManager = AuthManager()
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景渐变
                AngularGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "010101"), location: 0.00),
                        .init(color: Color(hex: "000000"), location: 0.0048),
                        .init(color: Color(hex: "2E4F17"), location: 0.3699),
                        .init(color: Color(hex: "2A370C"), location: 0.5240),
                        .init(color: Color(hex: "010101"), location: 1.0)
                    ]),
                    center: UnitPoint(x: 0.4281, y: 0.4891),
                    angle: Angle(degrees: 189.17)
                )
                .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 标题区域
                        VStack(spacing: 8) {
                            Text("注册账号")
                                .font(.largeTitle)
                                .fontWeight(.bold)
                                .foregroundColor(.white)
                            
                            Text("创建您的 CarbonCoin 账号")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                        .padding(.top, 40)
                        
                        // 表单区域
                        VStack(spacing: 16) {
                            // 用户ID输入框
                            VStack(alignment: .leading, spacing: 8) {
                                Text("用户ID")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                TextField("请输入用户ID", text: $authViewModel.userId)
                                    .textFieldStyle(CustomTextFieldStyle())
                                    .autocapitalization(.none)
                                    .disableAutocorrection(true)
                                
                                if let error = authViewModel.validationErrors["userId"] {
                                    Text(error)
                                        .font(.caption)
                                        .foregroundColor(.red)
                                }
                            }
                            
                            // 密码输入框
                            VStack(alignment: .leading, spacing: 8) {
                                Text("密码")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                HStack {
                                    if authViewModel.isPasswordVisible {
                                        TextField("请输入密码", text: $authViewModel.password)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    } else {
                                        SecureField("请输入密码", text: $authViewModel.password)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    }
                                    
                                    Button(action: {
                                        authViewModel.togglePasswordVisibility()
                                    }) {
                                        Image(systemName: authViewModel.isPasswordVisible ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                            .padding(.trailing, 8)
                                    }
                                }
                                
                                if let error = authViewModel.validationErrors["password"] {
                                    Text(error)
                                        .font(.caption)
                                        .foregroundColor(.red)
                                }
                            }
                            
                            // 确认密码输入框
                            VStack(alignment: .leading, spacing: 8) {
                                Text("确认密码")
                                    .font(.headline)
                                    .foregroundColor(.white)
                                
                                HStack {
                                    if authViewModel.isConfirmPasswordVisible {
                                        TextField("请再次输入密码", text: $authViewModel.confirmPassword)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    } else {
                                        SecureField("请再次输入密码", text: $authViewModel.confirmPassword)
                                            .textFieldStyle(CustomTextFieldStyle())
                                    }
                                    
                                    Button(action: {
                                        authViewModel.toggleConfirmPasswordVisibility()
                                    }) {
                                        Image(systemName: authViewModel.isConfirmPasswordVisible ? "eye.slash" : "eye")
                                            .foregroundColor(.gray)
                                            .padding(.trailing, 8)
                                    }
                                }
                                
                                if let error = authViewModel.validationErrors["confirmPassword"] {
                                    Text(error)
                                        .font(.caption)
                                        .foregroundColor(.red)
                                }
                            }
                        }
                        .padding(.horizontal, 24)
                        
                        // 按钮区域
                        VStack(spacing: 16) {
                            // 注册按钮
                            Button(action: {
                                Task {
                                    await authViewModel.register()
                                }
                            }) {
                                HStack {
                                    if authViewModel.isLoading {
                                        ProgressView()
                                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                            .scaleEffect(0.8)
                                    }
                                    Text("注册")
                                        .font(.headline)
                                        .foregroundColor(.white)
                                }
                                .frame(maxWidth: .infinity)
                                .frame(height: 50)
                                .background(
                                    authViewModel.isRegisterButtonEnabled ? Color(hex: "4B7905") : Color.gray
                                )
                                .cornerRadius(12)
                            }
                            .disabled(!authViewModel.isRegisterButtonEnabled)
                            
                            // 返回登录
                            HStack {
                                Text("已有账号？")
                                    .foregroundColor(.gray)
                                
                                Button(action: {
                                    dismiss()
                                }) {
                                    Text("立即登录")
                                        .foregroundColor(Color(hex: "4B7905"))
                                        .fontWeight(.medium)
                                }
                            }
                        }
                        .padding(.horizontal, 24)
                        
                        Spacer(minLength: 40)
                    }
                }
            }
            .navigationBarHidden(true)
        }
        .onAppear {
            authViewModel.authMode = .register
        }
        .onChange(of: authManager.isLoggedIn) { _, isLoggedIn in
            if isLoggedIn {
                dismiss()
            }
        }
        .onReceive(authViewModel.$successMessage) { message in
            if message != nil {
                // 注册成功后的处理
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                    dismiss()
                }
            }
        }
        .alert("错误", isPresented: .constant(authViewModel.errorMessage != nil)) {
            Button("确定") {
                authViewModel.clearMessages()
            }
        } message: {
            if let error = authViewModel.errorMessage {
                Text(error)
            }
        }
        .alert("成功", isPresented: .constant(authViewModel.successMessage != nil)) {
            Button("确定") {
                authViewModel.clearMessages()
            }
        } message: {
            if let success = authViewModel.successMessage {
                Text(success)
            }
        }
    }
}

// MARK: - 预览

struct RegisterView_Previews: PreviewProvider {
    static var previews: some View {
        RegisterView()
    }
}

#Preview {
    RegisterView()
}
