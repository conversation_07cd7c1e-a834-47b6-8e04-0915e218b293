//
//  PetView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

struct PetView: View {

    var body: some View {
        NavigationStack {
            ZStack {
                // 背景
                CustomAngularGradient()
                
                // 宠物列表
                PetListView()
            }
            .navigationTitle("宠物图鉴")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
    }
}

#Preview {
    let petViewModel = CarbonPetViewModel()
    PetView()
        .environmentObject(petViewModel)
}
