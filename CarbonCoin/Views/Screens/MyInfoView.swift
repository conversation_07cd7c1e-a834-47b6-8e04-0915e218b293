//
//  MyInfoView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI
import SwiftData

struct MyInfoView: View {
    @State private var shareLocation = true
    @State private var pushNotifications = true
    @State private var searchText = ""
    @State private var showLogoutAlert = false
    @StateObject private var authManager = AuthManager()

    var body: some View {
        NavigationStack {
            ZStack{
                CustomAngularGradient()
                
                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        // 用户信息卡片
                        NavigationLink{
                            SettingsView()
                        } label:{
                            UserProfileCard()
                        }
                        
                        // 好友管理
                        NavigationLink(destination: FriendshipListView()) {
                            FriendManagementSection(searchText: $searchText)
                        }
                        .buttonStyle(PlainButtonStyle())

                        // 登出按钮
                        LogoutSection(showLogoutAlert: $showLogoutAlert, authManager: authManager)

                        Spacer(minLength: 100) // 为底部TabBar留出空间
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.top, Theme.Spacing.md)
                }
            }
            .navigationTitle("我的")
            .navigationBarTitleDisplayMode(.large)
            .toolbarBackground(.clear, for: .navigationBar)
            .toolbarColorScheme(.dark, for: .navigationBar)
        }
        .alert("确认登出", isPresented: $showLogoutAlert) {
            Button("取消", role: .cancel) { }
            Button("登出", role: .destructive) {
                Task {
                    await authManager.logout()
                }
            }
        } message: {
            Text("您确定要登出当前账号吗？")
        }
    }
}

// MARK: - User Profile Card
struct UserProfileCard: View {
    @EnvironmentObject var appSettings: AppSettings
    
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \CarbonCoin.id, order: .forward) private var coins: [CarbonCoin]
    private var currentAmount: Int { coins.first?.amount ?? 0 }
    
    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 头像
            Circle()
                .fill(Color.primaryGradient)
                .frame(width: 60, height: 60)
                .overlay(
                    appSettings.settings.getAvatarSwiftUIImage()
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .clipShape(Circle())
                        .overlay(
                            Circle()
                                .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                        )
                )

            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text(appSettings.nickname)
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Text(appSettings.userId)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                HStack {
                    Image(systemName: "leaf.fill")
                        .foregroundColor(.auxiliaryYellow)
                        .font(.caption)

                    Text("碳币: \(currentAmount)")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }

            Spacer()

            Button(action: {}) {
                Image(systemName: "pencil")
                    .foregroundColor(.textSecondary)
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Friend Management Section
struct FriendManagementSection: View {
    @Binding var searchText: String

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            HStack {
                Text("好友管理")
                    .font(.title3Brand)
                    .foregroundColor(.textPrimary)

                Spacer()

                Image(systemName: "chevron.right")
                    .foregroundColor(.textSecondary)
                    .font(.caption)
            }

            // 好友功能预览
            VStack(spacing: Theme.Spacing.sm) {
                HStack {
                    Image(systemName: "person.2.fill")
                        .foregroundColor(.success)
                    Text("查看所有好友")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                    Spacer()
                }

                HStack {
                    Image(systemName: "person.badge.plus")
                        .foregroundColor(Color(hex: "4B7905"))
                    Text("添加新好友")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                    Spacer()
                }

                HStack {
                    Image(systemName: "person.badge.clock")
                        .foregroundColor(.auxiliaryYellow)
                    Text("处理好友请求")
                        .font(.bodyBrand)
                        .foregroundColor(.textPrimary)
                    Spacer()
                }
            }
        }
        .padding(Theme.Spacing.lg)
        .glassCard()
    }
}

// MARK: - Logout Section
struct LogoutSection: View {
    @Binding var showLogoutAlert: Bool
    let authManager: AuthManager

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            Button(action: {
                showLogoutAlert = true
            }) {
                HStack {
                    Image(systemName: "rectangle.portrait.and.arrow.right")
                        .foregroundColor(.red)

                    Text("登出账号")
                        .font(.bodyBrand)
                        .foregroundColor(.red)

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.textSecondary)
                        .font(.caption)
                }
                .padding(Theme.Spacing.lg)
            }
        }
        .glassCard()
    }
}

#Preview {
    MyInfoView()
        .stableBackground()
}
