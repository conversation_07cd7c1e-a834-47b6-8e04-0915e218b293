//
//  RecordEditView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/6.
//

import SwiftUI

// MARK: - 记录编辑视图

/// 记录编辑视图，用于编辑日志的描述、图片和其他信息
struct RecordEditView: View {

    // MARK: - Properties

    /// 要编辑的日志
    let log: UserLog

    /// 日志视图模型
    @ObservedObject var logViewModel: LogViewModel

    /// 编辑状态
    @State private var editedDescription: String
    @State private var editedImageList: [String]
    @State private var isPublic: Bool

    /// UI状态
    @State private var showImagePicker = false
    @State private var showImageCropper = false
    @State private var selectedImage: UIImage?
    @State private var croppedImage: UIImage?
    @State private var isUploading = false
    
    /// 预删除的图片列表
    @State private var pendingDeleteList: [String] = []

    /// 关闭视图
    @Environment(\.dismiss) private var dismiss

    // MARK: - Initialization

    init(log: UserLog, logViewModel: LogViewModel) {
        self.log = log
        self.logViewModel = logViewModel
        self._editedDescription = State(initialValue: log.description ?? "")
        self._editedImageList = State(initialValue: log.imageList ?? [])
        self._isPublic = State(initialValue: log.isPublic)
    }

    // MARK: - Body

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: Theme.Spacing.lg) {
                    // 描述编辑区域
                    descriptionEditSection

                    // 图片编辑区域
                    imageEditSection

                    // 活动组件区域（占位）
                    activityComponentSection

                    // 评论和点赞区域
                    commentsAndLikesSection
                }
                .padding(Theme.Spacing.lg)
            }
            .navigationTitle("日志编辑页")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("发布") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(isUploading)
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("发布") {
                        Task {
                            await saveChanges()
                        }
                    }
                    .disabled(isUploading)
                }
            }
            .globalBackground()
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedImage)
        }
        .sheet(isPresented: $showImageCropper) {
            if let selectedImage = selectedImage {
                ImageEditor.cropper(image: selectedImage) { croppedImage in
                    self.croppedImage = croppedImage
                    if let croppedImage = croppedImage {
                        Task {
                            await uploadImage(croppedImage)
                        }
                    }
                }
            }
        }
        .onChange(of: selectedImage) { _, newImage in
            if newImage != nil {
                showImageCropper = true
            }
        }
    }

    // MARK: - Description Edit Section

    private var descriptionEditSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("在此处添加更多有趣的文字内容...")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)

            TextField("输入你的想法", text: $editedDescription, axis: .vertical)
                .TextFieldBG()
                .lineLimit(3...10)
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                )
        )
    }

    // MARK: - Image Edit Section

    private var imageEditSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("添加当前状态")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)

            // 图片网格
            imageGridView
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                )
        )
    }

    private var imageGridView: some View {
        let imageSize: CGFloat = 160
        
        let columns = Array(repeating: GridItem(.flexible(), spacing: 2), count: 2)

        return LazyVGrid(columns: columns, spacing: 2) {
            // 现有图片
            ForEach(Array(editedImageList.enumerated()), id: \.offset) { index, imageURL in
                ZStack(alignment: .topTrailing) {
                    AsyncImage(url: URL(string: imageURL)) { image in
                        image
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: imageSize, height: imageSize)
                            .clipped()
                            .cornerRadius(Theme.CornerRadius.sm)
                    } placeholder: {
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                            .fill(Color.gray.opacity(0.2))
                            .frame(width: imageSize, height: imageSize)
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.8)
                            )
                    }

                    // 删除按钮
                    Button(action: {
                        let removed = editedImageList.remove(at: index)
                        pendingDeleteList.append(removed)
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.red)
                            .background(Color.white, in: Circle())
                    }
                    .offset(x: 8, y: -8)
                }
            }

            // 添加图片按钮
            if editedImageList.count < 4 {
                Button(action: {
                    showImagePicker = true
                }) {
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                        .fill(Color.gray.opacity(0.3))
                        .frame(width: 100, height: 100)
                        .overlay(
                            Image(systemName: "plus")
                                .font(.title2)
                                .foregroundColor(.textSecondary)
                        )
                }
                .disabled(isUploading)
            }
        }
    }

    // MARK: - Activity Component Section

    private var activityComponentSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            Text("在此处编辑你的事件卡片...")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)

            // 活动组件占位视图
            activityPlaceholderView
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                )
        )
    }

    private var activityPlaceholderView: some View {
        VStack(spacing: Theme.Spacing.md) {
            // 根据记录类型显示不同的占位组件
            switch log.recordType {
            case .trip:
                tripActivityPlaceholder
            case .location:
                locationActivityPlaceholder
            case .recognition:
                recognitionActivityPlaceholder
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                .fill(Color.gray.opacity(0.1))
        )
    }

    private var tripActivityPlaceholder: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack {
                Text("详情图")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
                Spacer()
            }

            // 地图
            FootprintDetailView(footprint: log.userFootprints!)

            // 统计信息
            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text("事件明细")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                HStack {
                    VStack(alignment: .leading) {
                        Text("减少碳排放量")
                            .font(.system(size: 12))
                            .foregroundColor(.textTertiary)
                        Text("0:54:01")
                            .font(.captionBrand)
                            .foregroundColor(.textPrimary)
                    }

                    Spacer()

                    VStack(alignment: .trailing) {
                        Text("本次出行时长")
                            .font(.system(size: 12))
                            .foregroundColor(.textTertiary)
                        Text("0:54:01")
                            .font(.captionBrand)
                            .foregroundColor(.textPrimary)
                    }
                }

                HStack {
                    VStack(alignment: .leading) {
                        Text("总行驶距离")
                            .font(.system(size: 12))
                            .foregroundColor(.textTertiary)
                        Text("12.5 KM")
                            .font(.captionBrand)
                            .foregroundColor(.textPrimary)
                    }

                    Spacer()

                    VStack(alignment: .trailing) {
                        Text("获得积分数量")
                            .font(.system(size: 12))
                            .foregroundColor(.textTertiary)
                        Text("1250")
                            .font(.captionBrand)
                            .foregroundColor(.textPrimary)
                    }
                }
            }

            // 发布位置
            HStack {
                Text("发布位置")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
                Spacer()
                Text("余杭区城市街道")
                    .font(.captionBrand)
                    .foregroundColor(.textPrimary)
                Button(action: {}) {
                    Image(systemName: "location.circle")
                        .foregroundColor(.accent)
                }
            }
        }
    }

    private var locationActivityPlaceholder: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack {
                Image(systemName: "location.fill")
                    .foregroundColor(.accent)
                Text("地点打卡活动组件")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                Spacer()
            }

            Text("此处显示地点相关的详细信息和统计数据")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.leading)
        }
    }

    private var recognitionActivityPlaceholder: some View {
        VStack(spacing: Theme.Spacing.sm) {
            HStack {
                Image(systemName: "viewfinder")
                    .foregroundColor(.accent)
                Text("卡片识别活动组件")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                Spacer()
            }

            Text("此处显示识别到的卡片信息和相关数据")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.leading)
        }
    }

    // MARK: - Comments and Likes Section

    private var commentsAndLikesSection: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.md) {
            // 点赞和评论统计
            HStack {
                // 点赞数
                HStack(spacing: 4) {
                    Image(systemName: "heart.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.red)
                    Text("\(logViewModel.getLikeCount(for: log.id))")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }

                Spacer()

                // 评论数
                HStack(spacing: 4) {
                    Image(systemName: "bubble.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.green)
                    Text("\(logViewModel.getCommentCount(for: log.id))")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                }
            }

            // 最近的评论预览
            if let comments = log.comments, !comments.isEmpty {
                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    Text("最近评论")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)

                    ForEach(comments.prefix(3)) { comment in
                        HStack(alignment: .top, spacing: Theme.Spacing.sm) {
                            Text(comment.user.nickname)
                                .font(.captionBrand)
                                .foregroundColor(.textSecondary)

                            Text(comment.content)
                                .font(.captionBrand)
                                .foregroundColor(.textPrimary)
                                .lineLimit(2)

                            Spacer()
                        }
                        .padding(.vertical, 2)
                    }
                }
            }
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                )
        )
    }

    // MARK: - Helper Methods

    /// 上传图片
    private func uploadImage(_ image: UIImage) async {
        isUploading = true

        do {
            guard let imageData = image.jpegData(compressionQuality: 0.8) else {
                print("❌ 图片数据转换失败")
                isUploading = false
                return
            }

            let imageURL = try await ImageShareService.shared.uploadImage(imageData)

            await MainActor.run {
                editedImageList.append(imageURL)
                isUploading = false
            }

            print("✅ 图片上传成功: \(imageURL)")

        } catch {
            await MainActor.run {
                isUploading = false
            }
            print("❌ 图片上传失败: \(error.localizedDescription)")
        }
    }

    /// 保存更改
    private func saveChanges() async {
        await logViewModel.updateUserLog(
            logId: log.id,
            imageList: editedImageList,
            description: editedDescription.isEmpty ? nil : editedDescription,
            isPublic: isPublic
        )

        // 检查是否有错误
        if logViewModel.errorMessage == nil {
            // 同时删除COS中的图片
            for url in pendingDeleteList {
                Task {
                    try? await ImageShareService.shared.deleteImage(url)
                }
            }
            pendingDeleteList.removeAll()
            
            dismiss()
        }
    }
}

// MARK: - Preview

#Preview {
    RecordEditView(
        log: UserLog(
            id: "1",
            userId: "user1",
            recordType: .trip,
            recordId: "trip1",
            imageList: [
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            description: "今天的骑行很愉快",
            isPublic: true,
            createdAt: Date(),
            updatedAt: Date(),
            user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
            likes: [],
            comments: [
                LogComment(id: "comment1", userId: "user2", content: "看起来很棒！", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user2", nickname: "朋友", avatarURL: nil))
            ],
            locationCheckIns: nil,
            userFootprints: nil,
            itemCard: nil
        ),
        logViewModel: LogViewModel()
    )
}
