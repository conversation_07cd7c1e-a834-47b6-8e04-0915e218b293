//  PetListView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

import SwiftUI

struct PetListView: View {
    @EnvironmentObject var petViewModel: CarbonPetViewModel
    
    private let columns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    var body: some View {
        ScrollView {
            LazyVGrid(columns: columns, spacing: 20) {
                ForEach(petViewModel.getOrderedDisplayModels()) { displayModel in
                    PetItemView(displayModel: displayModel)
                }
            }
            .padding()
        }
    }
}

#Preview {
    PetListView()
        .background(Color.black.ignoresSafeArea())
}
