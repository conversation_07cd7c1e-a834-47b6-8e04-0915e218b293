
//  CustomTabBar.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/15.
//

import SwiftUI

// MARK: - Tab Item Model
struct TabItem: Identifiable {
    let id = UUID()
    let iconName: String
    let title: String
    let tag: Int
}

// MARK: - Custom Tab Bar
struct CustomTabBar: View {
    @Binding var selectedTab: Int
    let tabItems: [TabItem]
    
    @Namespace private var animationNamespace // 添加命名空间
    
    var body: some View {
        ZStack(alignment: .bottom) {
            // 1. 黑色背景层
            RoundedRectangle(cornerRadius: Theme.TabBar.cornerRadius)
                .fill(Theme.TabBar.backgroundColor)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.TabBar.cornerRadius)
                        .stroke(Color.white.opacity(0.1), lineWidth: 1)
                )
                .frame(height: 70)
                .floatingShadow()
            
            // 2. 内容层，放在背景上面
            HStack(spacing: 0) {
                ForEach(tabItems) { item in
                    ZStack {
                        // 按钮本身
                        TabBarButton(
                            iconName: item.iconName,
                            title: item.title,
                            isSelected: selectedTab == item.tag,
                            action: {
                                withAnimation(Theme.AnimationStyle.bouncy) {
                                    selectedTab = item.tag
                                }
                            }
                        )
                        
                        // 选中状态的圆圈，使用 .overlay 和 .offset 来定位
                        if selectedTab == item.tag {
                            Circle()
                                .fill(Color.primaryGradient)
                                .frame(width: 60, height: 60)
                                .blur(radius: 1)
                                .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
                                .scaleEffect(selectedTab == item.tag ? 1.0 : 0.5)
                                .animation(Theme.AnimationStyle.slow, value: selectedTab)
                                .offset(y: -10)
                                .transition(.opacity) // 添加淡入淡出过渡
                                .matchedGeometryEffect(id: "selectedCircle", in: animationNamespace) // 几何匹配
                                .zIndex(-1)
                            Circle()
                                .fill(.black.opacity(0.8))
                                .frame(width: 77, height: 77)
                                .scaleEffect(selectedTab == item.tag ? 1.0 : 0.5)
                                .animation(Theme.AnimationStyle.slow, value: selectedTab)
                                .offset(y: -11)
                                .transition(.opacity) // 添加淡入淡出过渡
                                .matchedGeometryEffect(id: "selectedRing", in: animationNamespace) // 几何匹配
                                .zIndex(-2)
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
            }
            .padding(.horizontal, Theme.TabBar.horizontalPadding)
        }
        .frame(height: 79) // 整个视图的高度
    }
}

// MARK: - Tab Bar Button
struct TabBarButton: View {
    let iconName: String
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: action) {
            VStack() {
                // 图标
                Image(iconName)
                    .renderingMode(.template)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(
                        width: isSelected ? Theme.TabBar.selectedIconSize : Theme.TabBar.iconSize,
                        height: isSelected ? Theme.TabBar.selectedIconSize : Theme.TabBar.iconSize
                    )
                    .foregroundColor(isSelected ? .black : .white.opacity(0.8))
                    .scaleEffect(isPressed ? 0.9 : 1.0)
                    .offset(y: isSelected ? -10 : 0)
                    .animation(
                            isPressed
                                ? Theme.AnimationStyle.fast
                                : Theme.AnimationStyle.bouncy,
                            value: isSelected
                        )
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 60)
        .contentShape(Rectangle())
        
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Tab Bar Container
struct CustomTabBarContainer<Content: View>: View {
    @Binding var selectedTab: Int
    let tabItems: [TabItem]
    let content: Content
    
    init(selectedTab: Binding<Int>, tabItems: [TabItem], @ViewBuilder content: () -> Content) {
        self._selectedTab = selectedTab
        self.tabItems = tabItems
        self.content = content()
    }
    
    var body: some View {
        ZStack(alignment: .bottom) {
            // 主要内容
            content
                .background(Color.clear) // 确保内容背景透明
            
            // 自定义TabBar
            CustomTabBar(selectedTab: $selectedTab, tabItems: tabItems)
                .padding(.horizontal, Theme.Spacing.md)
        }
        .ignoresSafeArea(.keyboard, edges: .bottom)
        .background(Color.clear)
        .safeAreaInset(edge: .bottom) {
            Color.clear.frame(height: 0)
        }
    }
}

// MARK: - Preview
#Preview {
    @Previewable @State var selectedTab = 0
    
    let tabItems = [
        TabItem(iconName: "footIcon", title: "足迹", tag: 0),
        TabItem(iconName: "petIcon", title: "宠物", tag: 1),
        TabItem(iconName: "scanIcon", title: "扫码", tag: 2),
        TabItem(iconName: "chatIcon", title: "聊天", tag: 3),
        TabItem(iconName: "meIcon", title: "设置", tag: 4)
    ]
    
    return CustomTabBarContainer(selectedTab: $selectedTab, tabItems: tabItems) {
        ZStack {
            // 背景渐变
            Color.primaryGradient
                .ignoresSafeArea()
            
            VStack {
                Text("当前选中: \(selectedTab)")
                    .font(.title)
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding()
        }
    }
}

// MARK: - Preview
#Preview {
    @Previewable @State var selectedTab = 0
    
    let tabItems = [
        TabItem(iconName: "footIcon", title: "足迹", tag: 0),
        TabItem(iconName: "petIcon", title: "宠物", tag: 1),
        TabItem(iconName: "scanIcon", title: "扫码", tag: 2),
        TabItem(iconName: "chatIcon", title: "聊天", tag: 3),
        TabItem(iconName: "meIcon", title: "设置", tag: 4)
    ]
    
    return CustomTabBarContainer(selectedTab: $selectedTab, tabItems: tabItems) {
        ZStack {
            // 背景渐变
            Color.primaryGradient
                .ignoresSafeArea()
            
            VStack {
                Text("当前选中: \(selectedTab)")
                    .font(.title)
                    .foregroundColor(.white)
                
                Spacer()
            }
            .padding()
        }
    }
}
