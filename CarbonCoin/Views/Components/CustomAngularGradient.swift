//
//  CustomAngularGradient.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import SwiftUI

struct CustomAngularGradient: View {
    private let imageName: String

    init(imageName: String = "GradientBG") {
        self.imageName = imageName
    }

    var body: some View {
        GeometryReader { proxy in
            Color.clear   // 提供一个占位，保证安全区域行为
                .background(
                    Image(imageName)
                        .resizable()
                        .scaledToFill()
                        .opacity(0.6) // 渐变透明度可调
                        .ignoresSafeArea()
                )
        }
    }
}
