//
//  PropInteractionPopupView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

// MARK: - 道具交互弹窗提示视图

/// 道具交互弹窗提示视图，用于显示新收到的道具交互消息
struct PropInteractionPopupView: View {
    
    // MARK: - Properties
    
    let interaction: PropInteraction
    
    // MARK: - Body
    
    var body: some View {
        HStack(spacing: Theme.Spacing.md) {
            // 左侧：发送者头像
            senderAvatarView
            
            // 右侧：消息内容
            messageContentView
            
            Spacer()
        }
        .padding(Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(.ultraThinMaterial)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.brandGreen.opacity(0.3), lineWidth: 1)
                )
        )
        .shadow(color: .black.opacity(0.1), radius: 8, x: 0, y: 4)
        .frame(maxWidth: UIScreen.main.bounds.width * 0.85)
    }
    
    // MARK: - Subviews
    
    /// 发送者头像视图
    private var senderAvatarView: some View {
        AsyncImage(url: URL(string: interaction.sender.avatarURL ?? "")) { image in
            image
                .resizable()
                .aspectRatio(contentMode: .fill)
        } placeholder: {
            // 默认头像
            Image(systemName: "person.circle.fill")
                .font(.title2)
                .foregroundColor(.textSecondary)
        }
        .frame(width: 44, height: 44)
        .clipShape(Circle())
        .overlay(
            Circle()
                .stroke(Color.brandGreen.opacity(0.5), lineWidth: 2)
        )
    }
    
    /// 消息内容视图
    private var messageContentView: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
            // 发送者昵称
            Text(interaction.sender.nickname)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .fontWeight(.medium)
            
            // 消息文本
            Text("\(interaction.sender.nickname)给您发送了一条交互信息：\(interaction.displayRemark)")
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .lineLimit(2)
                .multilineTextAlignment(.leading)
            
            // 时间戳
            Text(formatInteractionTime(interaction.interactionTime))
                .font(.caption2)
                .foregroundColor(.textTertiary)
        }
    }
    
    // MARK: - Helper Methods
    
    /// 格式化交互时间
    private func formatInteractionTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        let now = Date()
        let timeInterval = now.timeIntervalSince(date)
        
        if timeInterval < 60 {
            return "刚刚"
        } else if timeInterval < 3600 {
            let minutes = Int(timeInterval / 60)
            return "\(minutes)分钟前"
        } else if timeInterval < 86400 {
            let hours = Int(timeInterval / 3600)
            return "\(hours)小时前"
        } else {
            formatter.dateFormat = "MM-dd HH:mm"
            return formatter.string(from: date)
        }
    }
}

// MARK: - Preview

#Preview {
    PropInteractionPopupView(
        interaction: PropInteraction(
            id: "preview-id",
            senderUserId: "sender123",
            receiverUserId: "receiver456",
            propId: 1,
            remark: "这是一个测试消息",
            interactionTime: Date(),
            receivedTime: nil,
            isRead: false,
            sender: PropInteractionUser(
                userId: "sender123",
                nickname: "测试用户",
                avatarURL: nil
            ),
            receiver: PropInteractionUser(
                userId: "receiver456",
                nickname: "接收者",
                avatarURL: nil
            )
        )
    )
    .padding()
    .background(Color.black)
    .preferredColorScheme(.dark)
}
