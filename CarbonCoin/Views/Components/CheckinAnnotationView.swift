//
//  CheckinAnnotationView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import SwiftUI

struct CheckinAnnotationView: View {
    let checkin: PlaceCheckin
    
    var body: some View {
        VStack(spacing: 4) {
            Text(checkin.formattedCreatedAt)
                .font(.caption2)
                .foregroundColor(.white.opacity(0.8))
            
            // 打卡图标
            Image("log-checkin")
                .resizable()
                .frame(width: 20, height: 20)
                .padding(8)
                .background(
                    Circle()
                        .fill(Color.brandGreen)
                        .shadow(color: .black.opacity(0.3), radius: 4, x: 0, y: 2)
                )

            // 地点名称（最多7个字符）
            Text(checkin.displayName.count > 7 ? String(checkin.displayName.prefix(7)) + "..." : checkin.displayName)
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.white)
                .padding(.horizontal, 8)
                .padding(.vertical, 4)
                .background(
                    Capsule()
                        .fill(Color.black.opacity(0.5))
                )
                .lineLimit(1)
        }
    }
}

#Preview {
    CheckinAnnotationView(checkin: .default)
        .preferredColorScheme(.dark)
}
