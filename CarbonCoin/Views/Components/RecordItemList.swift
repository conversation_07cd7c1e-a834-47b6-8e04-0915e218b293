//
//  RecordItemList.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import SwiftUI

// MARK: - 日志记录列表视图

/// 日志记录列表视图，包含三个标签页：碳足迹、日志、统计
struct RecordItemList: View {

    // MARK: - Properties

    /// 日志视图模型
    @StateObject private var logViewModel = LogViewModel()

    /// 当前用户ID
    let userId: String

    /// 当前选中的标签页
    @State private var selectedTab: TabType = .footprint

    /// 详情视图导航状态
    @State private var selectedLog: UserLog?
    @State private var showDetailView = false

    // MARK: - Tab Types

    enum TabType: String, CaseIterable {
        case footprint = "碳足迹"
        case logs = "日志"
        case statistics = "统计"
    }

    // MARK: - Initialization

    init(userId: String) {
        self.userId = userId
    }

    // MARK: - Body

    var body: some View {
        VStack(spacing: 0) {
            // 自定义标签栏
            customTabBar

            // 内容区域
            contentView
        }
        .padding(.leading, Theme.Spacing.md)
        .onAppear {
            loadData()
        }
        .navigationDestination(isPresented: $showDetailView) {
            if let selectedLog = selectedLog {
                RecordDetailView(log: selectedLog, logViewModel: logViewModel)
            }
        }
    }

    // MARK: - Custom Tab Bar

    private var customTabBar: some View {
        HStack(spacing: 0) {
            ForEach(TabType.allCases, id: \.self) { tab in
                Button(action: {
                    withAnimation(.easeInOut(duration: 0.3)) {
                        selectedTab = tab
                    }
                }) {
                    Text(tab.rawValue)
                        .font(.bodyBrand)
                        .foregroundColor(selectedTab == tab ? Color.textInvert : .textSecondary)
                        .padding(.vertical, Theme.Spacing.sm)
                        .padding(.horizontal, Theme.Spacing.lg)
                        .background(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .fill(selectedTab == tab ? Color.accentColor : Color.clear)
                        )
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(Theme.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                .fill(Color.cardBackground)
                .overlay(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                        .stroke(Color.gray.opacity(0.5), lineWidth: 1)
                )
        )
        .padding(.horizontal, Theme.Spacing.lg)
        .padding(.top, Theme.Spacing.md)
    }

    // MARK: - Content View

    private var contentView: some View {
        Group {
            switch selectedTab {
            case .footprint:
                footprintTabView
            case .logs:
                logsTabView
            case .statistics:
                statisticsTabView
            }
        }
        .padding(.top, Theme.Spacing.lg)
    }

    // MARK: - Footprint Tab View

    private var footprintTabView: some View {
        Group{
            if groupedLogs.isEmpty {
                EmptyFootprintView()
            } else {
                ScrollView {
                    ZStack(alignment: .topLeading) {
                        // 底层：时间轴
                        timelineAxisView()
                        
                        // 日期标签和日志项
                        LazyVStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                            ForEach(groupedLogs, id: \.date) { dayGroup in
                                VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                                    // 日期标签（胶囊）
                                    dateHeaderView(for: dayGroup.date)
                                    
                                    // 日志项
                                    ForEach(dayGroup.items) { log in
                                        // 日志内容
                                        RecordItem(log: log) {
                                            selectedLog = log
                                            showDetailView = true
                                        }
                                    }
                                }
                            }
                            
                            // 加载更多按钮
                            if logViewModel.hasMoreData && !logViewModel.isLoadingMore {
                                Button("加载更多") {
                                    Task { await logViewModel.loadMoreLogs() }
                                }
                                .buttonStyle(SecondaryButtonStyle())
                                .padding(.top, Theme.Spacing.lg)
                            }
                            
                            // 加载中指示器
                            if logViewModel.isLoadingMore {
                                ProgressView()
                                    .padding(.top, Theme.Spacing.lg)
                            }
                        }
                        .padding(.trailing, Theme.Spacing.lg)
                    }
                }
                .refreshable { await logViewModel.refreshLogs() }
            }
        }
    }


    // MARK: - Logs Tab View

    private var logsTabView: some View {
        Group {
            if publicLogs.isEmpty {
                EmptyLogsView()
            } else {
                ScrollView {
                    ZStack(alignment: .topLeading) {
                        // 底层：时间轴
                        timelineAxisView()

                        // 日期标签和日志项
                        LazyVStack(alignment: .leading, spacing: Theme.Spacing.lg) {
                            ForEach(groupedPublicLogs, id: \.date) { dayGroup in
                                VStack(alignment: .leading, spacing: Theme.Spacing.md) {
                                    // 日期标签（胶囊）
                                    dateHeaderView(for: dayGroup.date)

                                    // 日志详情项
                                    ForEach(dayGroup.items) { log in
                                        RecordPublicItem(
                                            log: log,
                                            logViewModel: logViewModel,
                                            currentUserId: userId
                                        )
                                    }
                                }
                            }

                            // 加载更多按钮
                            if logViewModel.hasMoreData && !logViewModel.isLoadingMore {
                                Button("加载更多") {
                                    Task { await logViewModel.loadMoreLogs() }
                                }
                                .buttonStyle(SecondaryButtonStyle())
                                .padding(.top, Theme.Spacing.lg)
                            }

                            // 加载中指示器
                            if logViewModel.isLoadingMore {
                                ProgressView()
                                    .padding(.top, Theme.Spacing.lg)
                            }
                        }
                        .padding(.trailing, Theme.Spacing.lg)
                    }
                }
                .refreshable { await logViewModel.refreshLogs() }
            }
        }
    }

    // MARK: - Statistics Tab View (暂时为空)

    private var statisticsTabView: some View {
        VStack {
            Spacer()
            Text("统计功能")
                .font(.title3Brand)
                .foregroundColor(.textSecondary)
            Text("敬请期待")
                .font(.bodyBrand)
                .foregroundColor(.textTertiary)
            Spacer()
        }
    }

    // MARK: - Date Header View

    private func dateHeaderView(for date: Date) -> some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 日期文本
            Text(formatDate(date))
                .font(.captionBrand)
                .foregroundColor(.textSecondary)
                .padding(.vertical, Theme.Spacing.xs)
                .padding(.horizontal, Theme.Spacing.sm)
                .background(
                    Capsule()
                        .fill(Color.cardBackground)
                        .overlay(
                            Capsule()
                                .stroke(Color.accent, lineWidth: 0.5)
                        )
                )

            Spacer()
        }
    }

    // MARK: - Timeline View

    private func timelineAxisView() -> some View {
        VStack(spacing: 0) {
            // 顶部留白
            Color.clear
                .frame(height: 20)

            // 时间轴竖线
            HStack {
                Spacer()
                    .frame(width: 6 + Theme.Spacing.md)
                
                Rectangle()
                    .fill(Color.accent.opacity(0.8))
                    .frame(width: 2)
                    .padding(.top, 2)
                
                Spacer()
            } // 从第一个圆点顶部开始

            Spacer()
        }
    }

    // MARK: - Helper Methods

    /// 加载数据
    private func loadData() {
        Task {
            await logViewModel.fetchUserLogs(userId: userId)
        }
    }

    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(date) {
            return "今天"
        } else if calendar.isDateInYesterday(date) {
            return "昨天"
        } else {
            formatter.dateFormat = "MM月dd日"
            return formatter.string(from: date)
        }
    }

    // MARK: - Computed Properties

    /// 按日期分组的日志
    private var groupedLogs: [DayGroup] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: logViewModel.userLogs) { log in
            calendar.startOfDay(for: log.createdAt)
        }

        return grouped.map { date, logs in
            DayGroup(date: date, items: logs.sorted { $0.createdAt > $1.createdAt })
        }.sorted { $0.date > $1.date }
    }

    /// 公开日志列表
    private var publicLogs: [UserLog] {
        return logViewModel.getPublicLogs()
    }

    /// 按日期分组的公开日志
    private var groupedPublicLogs: [DayGroup] {
        let calendar = Calendar.current
        let grouped = Dictionary(grouping: publicLogs) { log in
            calendar.startOfDay(for: log.createdAt)
        }

        return grouped.map { date, logs in
            DayGroup(date: date, items: logs.sorted { $0.createdAt > $1.createdAt })
        }.sorted { $0.date > $1.date }
    }
}

// MARK: - Day Group Model

/// 日期分组模型
struct DayGroup {
    let date: Date
    let items: [UserLog]
}

// MARK: - Preview

#Preview {
    NavigationStack {
        RecordItemList(userId: "testUser")
            .globalBackground()
    }
}
