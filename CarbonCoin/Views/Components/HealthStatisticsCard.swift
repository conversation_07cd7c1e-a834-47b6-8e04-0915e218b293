//
//  HealthStatisticsCard.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/19.
//

import SwiftUI

// MARK: - 健康数据统计卡片组件
struct HealthStatisticsCard: View {

    // MARK: - Properties
    let dataType: HealthDataType
    @ObservedObject var viewModel: HealthDataViewModel
    
    @State private var isAnimating = false

    // MARK: - Initialization
    init(dataType: HealthDataType, viewModel: HealthDataViewModel) {
        self.dataType = dataType
        self.viewModel = viewModel
    }
    
    // MARK: - Body
    var body: some View {
        Group {
            VStack(spacing: Theme.Spacing.lg) {
                headerView(viewModel: viewModel)
                Spacer()
                chartContainerView(viewModel: viewModel)
            }
            .padding(Theme.Spacing.lg)
            .glassCard()
            .refreshable {
                await viewModel.refreshData()
            }
            .onAppear{
                isAnimating = true
            }
            .onChange(of: viewModel.selectedPeriod) {
                isAnimating = false
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    isAnimating = true
                }
            }
        }
    }
    
    // MARK: - Header View
    private func headerView(viewModel: HealthDataViewModel) -> some View {
        HStack {
            CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                // 绑定已经处理了 selectedPeriod 的更新，这里只需要加载数据
                Task {
                    await viewModel.loadHealthData()
                }
            }
        }
    }
    
    // MARK: - Chart Container View
    private func chartContainerView(viewModel: HealthDataViewModel) -> some View {
        VStack(spacing: Theme.Spacing.md) {
            chartView(viewModel: viewModel)
            Spacer()
            statisticsView(viewModel: viewModel)
        }
    }
    
    // MARK: - Chart View
    private func chartView(viewModel: HealthDataViewModel) -> some View {
        VStack(spacing: Theme.Spacing.sm) {
            if viewModel.isLoading {
                ProgressView("加载中...")
                    .frame(height: 200)
            } else if let errorMessage = viewModel.errorMessage {
                VStack(spacing: Theme.Spacing.sm) {
                    Image(systemName: "exclamationmark.triangle")
                        .font(.title2)
                        .foregroundColor(.orange)
                    
                    Text(errorMessage)
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .frame(height: 200)
            } else {
                LineChartView(
                    healthData: viewModel.currentHealthData,
                    averageValue: Double(viewModel.getAverageValue()),
                    yAxisMin: Double(viewModel.yAxisMinValue),
                    yAxisMax: Double(viewModel.yAxisMaxValue),
                    selectedPeriod: viewModel.selectedPeriod
                )
                .frame(height: 200)
            }
        }
    }
    
    // MARK: - Statistics View
    private func statisticsView(viewModel: HealthDataViewModel) -> some View {
        HStack(alignment: .center ,spacing: Theme.Spacing.md) {
            VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                Text(viewModel.dataType.displayName + "统计")
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)

                if let statistics = viewModel.statistics {
                    HStack(spacing: Theme.Spacing.xs) {
                        Text("\(viewModel.getChangePercentageText())")
                            .font(.captionBrand)
                            .foregroundColor(viewModel.getChangePercentageColor())
                    }
                }
            }
            
            if let statistics = viewModel.statistics {
                // 根据数据类型显示不同的统计项
                switch viewModel.dataType {
                case .steps:
                    statisticItem(
                        title: "总步数",
                        value: formatToThousand(statistics.totalSteps),
                        icon: "figure.walk"
                    )

                    Divider()
                        .frame(height: 30)
                        .background(Color.textSecondary.opacity(0.3))

                    statisticItem(
                        title: "平均",
                        value: formatToThousand(Int(statistics.averageSteps)),
                        icon: "chart.line.uptrend.xyaxis"
                    )

                    Divider()
                        .frame(height: 30)
                        .background(Color.textSecondary.opacity(0.3))

                    statisticItem(
                        title: "最高",
                        value: formatToThousand(statistics.maxSteps),
                        icon: "arrow.up.circle"
                    )
                default:
                    // 对于其他数据类型，显示占位符
                    Text("暂未支持")
                        .font(.bodyBrand)
                        .foregroundColor(.textSecondary)
                }
            }
        }
        .padding(.horizontal, Theme.Spacing.xs)
        .padding(.vertical, Theme.Spacing.xl)
    }
    
    // MARK: - Statistic Item
    private func statisticItem(title: String, value: String, icon: String) -> some View {
        VStack(spacing: Theme.Spacing.xs) {
            HStack(spacing: Theme.Spacing.xs) {
                Image(systemName: icon)
                    .font(.caption)
                    .foregroundColor(.brandColor)
                
                Text(title)
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
            }
            .frame(height: 25)
            
            Text(value)
                .font(.bodyBrand)
                .foregroundColor(.textPrimary)
                .opacity(isAnimating ? 1 : 0.3)
                .offset(y: isAnimating ? 0 : 4)
                .animation(.easeOut(duration: 0.5), value: isAnimating)
        }
        .frame(maxWidth: .infinity)
        
    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
    
    /// 将统计数据转换以K为单位进行表示
    private func formatToThousand(_ number: Int) -> String{
        if( number >= 10000 ){
            let thousandValue = Double(number) / 1000  // 转成 Double 计算
            return String(format: "%.1fk", thousandValue)  // 保留1位小数
        } else {
            return "\(number)"
        }
    }
}

// MARK: - Compact Version
struct CompactHealthStatisticsCard: View {

    // MARK: - Properties
    let dataType: HealthDataType
    @ObservedObject var viewModel: HealthDataViewModel

    // MARK: - Initialization
    init(dataType: HealthDataType, viewModel: HealthDataViewModel) {
        self.dataType = dataType
        self.viewModel = viewModel
    }
    
    var body: some View {
        Group {
                VStack(spacing: Theme.Spacing.sm) {
                    HStack {
                        Text(viewModel.dataType.displayName)
                            .font(.bodyBrand)
                            .foregroundColor(.textPrimary)

                        Spacer()

                        CompactTimePeriodSelector(selectedPeriod: $viewModel.selectedPeriod) { period in
                            // 绑定已经处理了 selectedPeriod 的更新，这里只需要加载数据
                            Task {
                                await viewModel.loadHealthData()
                            }
                        }
                    }

                    if let statistics = viewModel.statistics {
                        // 根据数据类型显示不同的内容
                        switch viewModel.dataType {
                        case .steps:
                            HStack {
                                VStack(alignment: .leading, spacing: 2) {
                                    Text("\(formatNumber(Int(statistics.averageSteps)))")
                                        .font(.title2Brand)
                                        .foregroundColor(.textPrimary)

                                    Text("平均步数")
                                        .font(.captionBrand)
                                        .foregroundColor(.textSecondary)
                                }

                                Spacer()

                                Text(viewModel.getChangePercentageText())
                                    .font(.captionBrand)
                                    .foregroundColor(viewModel.getChangePercentageColor())
                            }
                        default:
                            // 对于其他数据类型，显示占位符
                            Text("暂未支持")
                                .font(.bodyBrand)
                                .foregroundColor(.textSecondary)
                        }
                    }
                }
                .padding(Theme.Spacing.md)
                .glassCard()
            }

    }
    
    // MARK: - Helper Methods
    private func formatNumber(_ number: Int) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .decimal
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: NSNumber(value: number)) ?? "\(number)"
    }
}

// MARK: - Preview
#Preview {
    let healthManager = HealthManager()
    let viewModel = HealthDataViewModel(dataType: .steps, healthManager: healthManager)
    VStack(spacing: 20) {
        HealthStatisticsCard(dataType: .steps, viewModel: viewModel)
        CompactHealthStatisticsCard(dataType: .steps, viewModel: viewModel)
    }
    .padding()
    .stableBackground()
    .environmentObject(HealthManager())
}
