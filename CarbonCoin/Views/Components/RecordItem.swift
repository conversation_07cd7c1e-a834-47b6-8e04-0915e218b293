//
//  RecordItem.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/3.
//

import SwiftUI

// MARK: - 日志记录项视图

/// 日志记录项视图，仅支持精简版显示模式
struct RecordItem: View {

    // MARK: - Properties

    /// 日志数据
    let log: UserLog

    /// 点击回调
    let onTap: (() -> Void)?

    // MARK: - Initialization

    init(log: UserLog, onTap: (() -> Void)? = nil) {
        self.log = log
        self.onTap = onTap
    }

    // MARK: - Body

    var body: some View {
        compactView
    }

    // MARK: - Compact View

    private var compactView: some View {
        Button(action: {
            onTap?()
        }) {
            HStack(spacing: Theme.Spacing.md) {
                // 左侧图标
                iconView

                Spacer()

                // 右侧内容
                VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                    // 日期和描述
                    titleAndDescriptionView

                    // 时间和统计信息
                    timeAndStatsView
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(Theme.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                        .fill(Color.recortItemBG)
                        .overlay(
                            RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                .stroke(Color.recortItemBG.opacity(0.5), lineWidth: 1)
                        )
                )
            }
            .padding(.trailing, Theme.Spacing.md)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - Icon View

    private var iconView: some View {
        ZStack {
            if log.isPublic {
                // public 下：渐变圆圈
                Circle()
                    .fill(
                        LinearGradient(
                            gradient: Gradient(stops: [
                                .init(color: Color(red: 0.38, green: 0.84, blue: 0.73), location: 0.0),
                                .init(color: Color(red: 0.69, green: 0.92, blue: 0.4), location: 1.0)
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .frame(width: 48, height: 48)
            } else {
                // public：保持单色 accent 背景
                Circle()
                    .fill(Color.accent)
                    .frame(width: 48, height: 48)
            }

            // 图标
            Image(ActivityUtils.getIconName(for: log))
                .resizable()
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)
        }
    }

    // MARK: - Title and Description View

    private var titleAndDescriptionView: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack(spacing: 4) {
                // 时间
                Text(timeText)
                    .font(.captionBrand)
                    .foregroundColor(.textTertiary)
                
                Spacer()
                
                // 位置信息
                if let locationText = locationText {
                    Text(locationText)
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                        .lineLimit(1)
                        .padding(.trailing, 4)
                }
            }
            
            // 描述文本
            if let description = log.description, !description.isEmpty {
                Text(description)
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                    .lineLimit(2)
            }
        }
    }

    // MARK: - Location View

    private var locationView: some View {
        Group {
           
        }
    }

    // MARK: - Time and Stats View

    private var timeAndStatsView: some View {
        HStack(spacing: Theme.Spacing.sm) {


            Spacer()

            // 统计信息（点赞、评论等）
            statsView
        }
    }

    // MARK: - Stats View

    private var statsView: some View {
        HStack(spacing: Theme.Spacing.sm) {
            // 点赞数
            if let likes = log.likes, !likes.isEmpty {
                HStack(spacing: 2) {
                    Image(systemName: "heart")
                        .font(.caption)
                        .foregroundColor(.red.opacity(0.5))
                    Text("\(likes.count)")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }

            // 评论数
            if let comments = log.comments, !comments.isEmpty {
                HStack(spacing: 2) {
                    Image(systemName: "bubble")
                        .font(.caption)
                        .foregroundColor(.textTertiary)
                    Text("\(comments.count)")
                        .font(.captionBrand)
                        .foregroundColor(.textTertiary)
                }
            }
        }
    }

    // MARK: - Computed Properties

    /// 位置文本
    private var locationText: String? {
        switch log.recordType {
        case .location:
            if let checkin = log.locationCheckIns {
                return checkin.position
            }
            return nil
        case .recognition:
            if let card = log.itemCard {
                return card.location
            }
            return nil
        case .trip:
            return "足迹记录" // 足迹记录显示固定文本
        }
    }

    /// 时间文本
    private var timeText: String {
        let formatter = DateFormatter()
        formatter.locale = Locale(identifier: "zh_CN")

        let calendar = Calendar.current
        if calendar.isDateInToday(log.createdAt) {
            formatter.dateFormat = "今天 HH:mm"
        } else if calendar.isDateInYesterday(log.createdAt) {
            formatter.dateFormat = "昨天 HH:mm"
        } else {
            formatter.dateFormat = "MM月dd日 HH:mm"
        }

        return formatter.string(from: log.createdAt)
    }
}

// MARK: - Preview

#Preview {
    VStack(spacing: Theme.Spacing.md) {
        // 地点打卡示例
        RecordItem(
            log: UserLog(
                id: "1",
                userId: "user1",
                recordType: .location,
                recordId: "checkin1",
                imageList: nil,
                description: "和其余2项茶饮类消费",
                isPublic: false,
                createdAt: Date(),
                updatedAt: Date(),
                user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                likes: Array(repeating: LogLike(id: "like1", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)), count: 30),
                comments: Array(repeating: LogComment(id: "comment1", userId: "user3", content: "不错", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil)), count: 10),
                locationCheckIns: LocationCheckIn(id: "checkin1", position: "余杭区城市街道", latitude: 30.0, longitude: 120.0, createdAt: Date()),
                userFootprints: nil,
                itemCard: nil
            )
        )

        // 公开足迹记录示例
        RecordItem(
            log: UserLog(
                id: "2",
                userId: "user1",
                recordType: .trip,
                recordId: "trip1",
                imageList: nil,
                description: "和其余2项茶饮类消费",
                isPublic: true,
                createdAt: Calendar.current.date(byAdding: .day, value: -1, to: Date()) ?? Date(),
                updatedAt: Date(),
                user: UserInfo(userId: "user1", nickname: "测试用户", avatarURL: nil),
                likes: Array(repeating: LogLike(id: "like2", userId: "user2", createdAt: Date(), user: UserInfo(userId: "user2", nickname: "用户2", avatarURL: nil)), count: 30),
                comments: Array(repeating: LogComment(id: "comment2", userId: "user3", content: "不错", replyTo: nil, createdAt: Date(), user: UserInfo(userId: "user3", nickname: "用户3", avatarURL: nil)), count: 10),
                locationCheckIns: nil,
                userFootprints: nil,
                itemCard: nil
            )
        )
    }
    .padding()
    .globalBackground()
}
