//
//  activityComponent.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/7.
//

import Foundation
import SwiftUI


// MARK: 活动组件入口
struct activityComponentView: View {
    let log: UserLog

    var body: some View{
        VStack {
            switch log.recordType {
            case .trip:
                if let footprint = log.userFootprints {
                    tripActivityView(footprint: footprint)
                }
            case .location:
                locationActivityView
            case .recognition:
                recognitionActivityView(log: log)
            }
        }
    }
}

// MARK: 根据分类显示组件
struct tripActivityView: View {
    let footprint: UserFootprints
    
    var body: some View {
        NavigationLink(destination: FootprintDetailView(footprint: footprint)){
                    HStack(spacing: Theme.Spacing.md) {
                        // 出行方式图标
                        Image(getIconName(footprint.activityType))
                            .renderingMode(.template)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: Theme.IconSize.md, height: Theme.IconSize.md)
                            .foregroundColor(.brand)
                            .padding(8)
                            .background(Color.brand.opacity(0.1))
                            .clipShape(Circle())
                        
                        // 出行信息
                        VStack(alignment: .leading, spacing: Theme.Spacing.xs) {
                            HStack {
                                Text(footprint.activityType.displayName)
                                    .font(.bodyBrand)
                                    .foregroundColor(.textPrimary)
                                
                                Spacer()
                                
                                Text(footprint.formattedCreatedAt)
                                    .font(.captionBrand)
                                    .foregroundColor(.textSecondary)
                            }
                            
                            HStack {
                                Text("距离: \(footprint.formattedDistance)")
                                    .font(.captionBrand)
                                    .foregroundColor(.textSecondary)
                                
                                Spacer()
                                
                                Text("时长: \(footprint.formattedDuration)")
                                    .font(.captionBrand)
                                    .foregroundColor(.textSecondary)
                                
                                // 完成状态
                                if footprint.isFinished {
                                    Image(systemName: "checkmark.circle.fill")
                                        .foregroundColor(.success)
                                        .font(.caption)
                                } else {
                                    Image(systemName: "clock.fill")
                                        .foregroundColor(.warning)
                                        .font(.caption)
                                }
                            }
                        }
                    }
                    .padding(Theme.Spacing.md)
                    .background(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                            .fill(Color.cardBackground.opacity(0.5))
                            .overlay(
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                                    .stroke(Color.textSecondary.opacity(0.2), lineWidth: 1)
                            )
                    )
                }
                .buttonStyle(.plain)
        }
    
        private func getIconName(_ activityType: ActivityType) -> String {
            switch activityType {
            case .walking:
                return "log-walk"
            case .cycling:
                return "log-cycling"
            case .bus:
                return "log-bus"
            case .subway:
                return "log-subway"
            }
        }
    
}

var locationActivityView: some View {
    HStack {
        Image(systemName: "location.fill")
            .foregroundColor(.accent)
        Text("地点活动组件")
            .font(.captionBrand)
            .foregroundColor(.textSecondary)
        Spacer()
    }
    .padding(.vertical, Theme.Spacing.xs)
}

// MARK: 识别活动组件
struct recognitionActivityView: View {
    let log: UserLog
    @StateObject private var userItemCardViewModel = UserItemCardViewModel()
    @State private var recentUserItemCard: UserItemCard?
    @State private var showCardDetail = false
    @AppStorage("currentUserId") private var currentUserId: String = ""

    var body: some View {
        VStack(alignment: .leading, spacing: Theme.Spacing.sm) {
            // 活动标题
            HStack {
                Image(systemName: "viewfinder")
                    .foregroundColor(.brandGreen)
                    .font(.title3)
                    .padding(.leading, 4)

                Text("最近创建的卡片")
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)

                Spacer()

                // 时间戳
                Text(formatDate(log.createdAt))
                    .font(.captionBrand)
                    .foregroundColor(.textSecondary)
                    .padding(.trailing, 4)
            }

            // 卡片内容
            if let userItemCard = recentUserItemCard {
                Button(action: {
                    showCardDetail = true
                }) {
                    ItemCardView(userItemCard: userItemCard, displayStyle: .compact)
                }
                .buttonStyle(PlainButtonStyle())
            } else {
                // 占位内容
                HStack {
                    Image(systemName: "photo.badge.plus")
                        .foregroundColor(.textSecondary)
                        .font(.title2)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("暂无最近创建的卡片")
                            .font(.bodyBrand)
                            .foregroundColor(.textSecondary)

                        Text("使用主体提取功能创建您的第一张卡片")
                            .font(.captionBrand)
                            .foregroundColor(.textSecondary.opacity(0.8))
                    }

                    Spacer()
                }
                .padding(.vertical, Theme.Spacing.sm)
                .padding(.horizontal, Theme.Spacing.md)
                .background(
                    RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                        .fill(Color.cardBackground.opacity(0.3))
                )
            }
        }
        .padding(.vertical, Theme.Spacing.xs)
        .onAppear {
            loadRecentCard()
        }
        .sheet(isPresented: $showCardDetail) {
            if let userItemCard = recentUserItemCard {
                cardDetailSheet(userItemCard)
            }
        }
    }

    // MARK: - 卡片详情Sheet
    private func cardDetailSheet(_ userItemCard: UserItemCard) -> some View {
        NavigationStack {
            ZStack {
                CustomAngularGradient()

                ScrollView {
                    VStack(spacing: Theme.Spacing.lg) {
                        ItemCardView(userItemCard: userItemCard, displayStyle: .full)
                            .padding(.horizontal, Theme.Spacing.md)
                    }
                    .padding(.top, Theme.Spacing.lg)
                    .padding(.bottom, Theme.Spacing.xl)
                }
                .scrollContentBackground(.hidden)
            }
            .navigationTitle("卡片详情")
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarBackButtonHidden(true)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("关闭") {
                        showCardDetail = false
                    }
                    .font(.bodyBrand)
                    .foregroundColor(.textPrimary)
                }
            }
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
    }

    // MARK: - 数据加载方法
    private func loadRecentCard() {
        Task {
            await userItemCardViewModel.fetchUserItemCards(for: currentUserId)

            // 获取最近创建的卡片（按获取时间排序）
            await MainActor.run {
                recentUserItemCard = userItemCardViewModel.userItemCards
                    .filter { $0.isAuthor } // 只显示用户创建的卡片
                    .sorted { $0.acquiredAt > $1.acquiredAt }
                    .first
            }
        }
    }

    // MARK: - 辅助方法
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH:mm"
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter.string(from: date)
    }
}
