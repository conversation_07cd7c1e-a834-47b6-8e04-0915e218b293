//
//  TimePeriodSelector.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/16.
//

import SwiftUI

// MARK: - 时间段选择器组件
struct TimePeriodSelector: View {
    
    // MARK: - Properties
    @Binding var selectedPeriod: TimePeriod
    let onSelectionChanged: ((TimePeriod) -> Void)?
    
    // MARK: - State
    @State private var animationOffset: CGFloat = 0
    
    // MARK: - Initialization
    init(selectedPeriod: Binding<TimePeriod>, onSelectionChanged: ((TimePeriod) -> Void)? = nil) {
        self._selectedPeriod = selectedPeriod
        self.onSelectionChanged = onSelectionChanged
    }
    
    // MARK: - Body
    var body: some View {
        HStack(spacing: Theme.Spacing.sm) {
            ForEach(TimePeriod.allCases) { period in
                periodButton(for: period)
            }
        }
        .padding(.horizontal, Theme.Spacing.xs)
        .padding(.vertical, Theme.Spacing.xs)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                .fill(Color.defaultButtonBackground.opacity(0.6))
        )
    }
    
    // MARK: - Period Button
    private func periodButton(for period: TimePeriod) -> some View {
        Button(action: {
            print("我在button这里")
            selectPeriod(period)
        }) {
            Text(period.displayName)
                .font(.captionBrand)
                .foregroundColor(selectedPeriod == period ? .white : .textSecondary)
                .padding(.horizontal, Theme.Spacing.md)
                .padding(.vertical, Theme.Spacing.sm)
                .background(
                    Group {
                        if selectedPeriod == period {
                            // 选中状态：渐变背景
                            selectedBackgroundView
                        } else {
                            // 未选中状态：透明背景
                            Color.clear
                        }
                    }
                )
                .clipShape(RoundedRectangle(cornerRadius: Theme.CornerRadius.sm))
                .scaleEffect(selectedPeriod == period ? 1.05 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.3), value: selectedPeriod)
    }
    
    // MARK: - Selected Background View
    private var selectedBackgroundView: some View {
        RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
            .fill(
                LinearGradient(
                    gradient: Gradient(stops: [
                        .init(color: Color(hex: "61D7B9"), location: 0.0),
                        .init(color: Color(hex: "B0EB67"), location: 1.0)
                    ]),
                    startPoint: .top,
                    endPoint: .bottom
                )
            )
            .overlay(
                RoundedRectangle(cornerRadius: Theme.CornerRadius.sm)
                    .stroke(
                        LinearGradient(
                            gradient: Gradient(colors: [
                                Color.white.opacity(0.3),
                                Color.clear
                            ]),
                            startPoint: .top,
                            endPoint: .bottom
                        ),
                        lineWidth: 1
                    )
            )
            .shadow(
                color: Color(hex: "61D7B9").opacity(0.3),
                radius: 8,
                x: 0,
                y: 4
            )
    }
    
    // MARK: - Methods
    
    /// 选择时间段
    private func selectPeriod(_ period: TimePeriod) {
        // 添加触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
        
        // 更新选中状态
        withAnimation(.easeInOut(duration: 0.3)) {
            selectedPeriod = period
        }
        
        // 调用回调（即使选择相同的时间段也要调用，因为外部可能需要重新加载数据）
        onSelectionChanged?(period)
    }
}

// MARK: - Compact Time Period Selector
/// 紧凑版时间段选择器（用于较小的空间）
struct CompactTimePeriodSelector: View {
    
    @Binding var selectedPeriod: TimePeriod
    let onSelectionChanged: ((TimePeriod) -> Void)?
    
    init(selectedPeriod: Binding<TimePeriod>, onSelectionChanged: ((TimePeriod) -> Void)? = nil) {
        self._selectedPeriod = selectedPeriod
        self.onSelectionChanged = onSelectionChanged
    }
    
    var body: some View {
        HStack(spacing: 4) {
            ForEach(TimePeriod.allCases) { period in
                Button(action: {
                    selectPeriod(period)
                    onSelectionChanged?(period)
                }) {
                    Text(period.displayName)
                        .font(.system(size: 13, weight: .semibold))
                        .frame(minWidth: 60)
                        .foregroundColor(selectedPeriod == period ? .textInvert.opacity(0.8): .textSecondary)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 8)
                        .background(
                            Group {
                                if selectedPeriod == period {
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color.brandColor)
                                } else {
                                    RoundedRectangle(cornerRadius: 16)
                                        .fill(Color.clear)
                                }
                            }
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .id( period.id )
            }
        }
        .padding(4)
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(Color.defaultButtonBackground.opacity(0.5))
        )
        .animation(.easeInOut(duration: 0.2), value: selectedPeriod)
    }
    
    private func selectPeriod(_ period: TimePeriod) {
        // 即使选择相同的时间段，也要触发回调，因为外部可能需要重新加载数据
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()

        print("[CompactTimePeriodSelector] selectPeriod called with: \(period), current: \(selectedPeriod)")

        selectedPeriod = period
        print("[CompactTimePeriodSelector] selectedPeriod updated to: \(selectedPeriod)")
        onSelectionChanged?(period)
        print("[CompactTimePeriodSelector] onSelectionChanged callback called")
    }
}

// MARK: - Preview
#Preview("标准选择器") {
    VStack(spacing: 20) {
        TimePeriodSelector(selectedPeriod: .constant(.week))
        
        TimePeriodSelector(selectedPeriod: .constant(.month))
        
        CompactTimePeriodSelector(selectedPeriod: .constant(.day))
    }
    .padding()
    .stableBackground()
}

#Preview("交互式选择器") {
    struct InteractivePreview: View {
        @State private var selectedPeriod: TimePeriod = .week
        
        var body: some View {
            VStack(spacing: 30) {
                Text("当前选择: \(selectedPeriod.displayName)")
                    .font(.title2Brand)
                    .foregroundColor(.textPrimary)
                
                TimePeriodSelector(selectedPeriod: $selectedPeriod) { period in
                    print("选择了: \(period.displayName)")
                }
                
                CompactTimePeriodSelector(selectedPeriod: $selectedPeriod) { period in
                    print("紧凑选择器选择了: \(period.displayName)")
                }
            }
            .padding()
        }
    }
    
    return InteractivePreview()
        .stableBackground()
}
