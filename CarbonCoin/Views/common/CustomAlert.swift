//
//  CustomAlert.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/1.
//

import SwiftUI

struct CustomAlert: View {
    @Binding var isPresented: Bool
    var title: String
    var message: String
    var confirmTitle: String = "确认"
    var cancelTitle: String = "取消"
    var onConfirm: () -> Void = {}
    var onCancel: () -> Void = {}
    
    var body: some View {
        if isPresented {
            ZStack {
                // 半透明背景
                Color.black.opacity(0.4)
                    .ignoresSafeArea()
                    .onTapGesture {
                        dismiss(cancel: true)
                    }
                
                // 弹窗内容
                VStack(spacing: 16) {
                    Text(title)
                        .font(.headline)
                        .padding(.top, 8)
                    
                    Text(message)
                        .font(.subheadline)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal)
                    
                    Divider()
                    
                    HStack {
                        Button(cancelTitle) {
                            dismiss(cancel: true)
                        }
                        .frame(maxWidth: .infinity)
                        
                        Divider()
                            .frame(height: 20)
                        
                        <PERSON><PERSON>(confirmTitle) {
                            dismiss(cancel: false)
                            onConfirm()
                        }
                        .frame(maxWidth: .infinity)
                    }
                }
                .padding()
                .background(RoundedRectangle(cornerRadius: 16).fill(.brand.opacity(0.2)))
                .background(.ultraThinMaterial, in: RoundedRectangle(cornerRadius: 20, style: .continuous))
                .frame(maxWidth: 300)
                .shadow(radius: 10)
                .transition(.scale)
            }
            .animation(.easeInOut, value: isPresented)
        }
    }
    
    private func dismiss(cancel: Bool) {
        withAnimation {
            isPresented = false
        }
        if cancel {
            onCancel()
        }
    }
}

#Preview("CustomAlert Demo"){
    StatefulPreviewWrapper(true) { binding in
        ZStack {
            Color.gray.ignoresSafeArea()
            CustomAlert(
                isPresented: binding,
                title: "确认删除？",
                message: "此操作不可撤销。",
                confirmTitle: "删除",
                cancelTitle: "取消",
                onConfirm: { print("✅ 确认") },
                onCancel: { print("❌ 取消") }
            )
            .environment(\.colorScheme, .dark)
        }
    }
}
