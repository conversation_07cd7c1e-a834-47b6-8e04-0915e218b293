//
//  PetDetailView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/17.
//

// TODO: 滑动切换视图时的页面效果；箭头的左右对齐；背景图的替换
import SwiftUI

struct PetDetailView: View {
    @State private var currentDisplayModel: PetDisplayModel
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject  var petViewModel: CarbonPetViewModel
    
    @State private var isAnimating = false
    
    init(displayModel: PetDisplayModel) {
            _currentDisplayModel = State(initialValue: displayModel)
    }

    var body: some View {
        ZStack {
            // 背景
            CustomAngularGradient()

            ScrollView {
                VStack(spacing: 0) {
                    // 上半部分：宠物展示区域
                    PetDisplaySection(
                        displayModel: currentDisplayModel,
                        onPrevious: {
                            if let previous = petViewModel.getPreviousDisplayModel(current: currentDisplayModel){
                                currentDisplayModel = previous
                            }
                        },
                        onNext: {
                            if let next = petViewModel.getNextDisplayModel(current: currentDisplayModel){
                                currentDisplayModel = next
                            }
                        }
                    )

                    // 下半部分：装扮信息区域
                    PetOutfitSection()
                        .padding(.top, 90)
                        // 使用 UIScreen 实现屏幕宽度比例的约束
                        .frame(width: UIScreen.main.bounds.width * 0.97)
                        .opacity(isAnimating ? 1: 0)
                        .offset(y: isAnimating ? 0: 40)
                        .animation(.easeOut(duration: 0.5), value: isAnimating)
                }
            }
            .scrollContentBackground(.hidden)
        }
        .onAppear{
            isAnimating = true
        }
        .navigationBarBackButtonHidden(true)
        .toolbar {
            ToolbarItem(placement: .navigationBarLeading) {
                Button(action: { dismiss() }) {
                    Image(systemName: "chevron.left")
                        .font(.title2)
                        .foregroundColor(.white)
                        .background(
                            Circle()
                                .fill(Color.black.opacity(0.3))
                                .frame(width: 40, height: 40)
                        )
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                HStack(spacing: 8) {
                    // 碳币显示
                    HStack(spacing: 4) {
                        Image(systemName: "leaf.fill")
                            .foregroundColor(Color(hex: "B0EB67"))
                            .font(.caption)
                        Text("32.2k")
                            .font(.captionBrand)
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 12)
                    .padding(.vertical, 6)
                    .background(
                        Capsule()
                            .fill(Color.black.opacity(0.3))
                    )
                }
            }
        }
        .toolbarBackground(.clear, for: .navigationBar)
        .toolbarColorScheme(.dark, for: .navigationBar)
    }
}

// MARK: - 宠物展示区域
struct PetDisplaySection: View {
    let displayModel: PetDisplayModel
    let onPrevious: () -> Void
    let onNext: () -> Void
    

    var body: some View {
        ZStack {
            // 背景占位区域（为后续背景图预留空间）
            
            if let bgImage = UIImage(named: "petDetail_bg"){
                Image(uiImage:bgImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 350)
                    .opacity(0.4)
                    .padding(.horizontal, Theme.Spacing.md)
                    .offset(y: -80)
            }else{
                RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                    .fill(Color.black.opacity(0.2))
                    .frame(height: 400)
                    .padding(.horizontal, Theme.Spacing.md)
            }
           

            VStack(spacing: Theme.Spacing.md) {
                Spacer()

                // 宠物图片
                if let petImage = UIImage(named: displayModel.template.imageName) {
                    Image(uiImage: petImage)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 270, height: 300)
                        .scaleEffect(1) // 放大显示
                        .offset(y: 20)
                }

                Spacer()

                // 底部信息区域
                PetInfoBottomSection(
                    displayModel: displayModel,
                    onPrevious: onPrevious,
                    onNext: onNext
                )
            }
            .frame(height: 500)
            .padding(.horizontal, Theme.Spacing.md)
        }
    }
}

// MARK: - 宠物信息底部区域
struct PetInfoBottomSection: View {
    let displayModel: PetDisplayModel
    let onPrevious: () -> Void
    let onNext: () -> Void

    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            HStack {
                // 中间宠物信息
                VStack(spacing: Theme.Spacing.sm) {
                    HStack(spacing: 8){
                        // 等级信息
                        Text("Lv\(displayModel.displayLevel)")
                            .font(.title3Brand)
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(
                                RoundedRectangle(cornerRadius: Theme.CornerRadius.lg)
                                    .fill(Color.primaryGradient.opacity(0.9))
                            )
                        
                        // 宠物名称
                        Text(displayModel.template.name)
                            .font(.title1Brand)
                            .foregroundColor(.white)

                        // 星级评价
                        HStack(spacing: 4) {
                            ForEach(0..<displayModel.template.rarity, id: \.self) { _ in
                                Image("star")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(width: 24, height: 24)
                            }
                        }
                    }
                   

                    // 描述信息和切换箭头
                    GeometryReader{geo in
                        HStack(spacing: Theme.Spacing.md) {
                            // 左箭头
                            Button(action: onPrevious ) {
                                Image("left_arrow")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(height: 140)
                            }
                            

                            // 描述文字
                            Text(displayModel.template.introduction )
                                .font(.bodyBrand)
                                .foregroundColor(.white.opacity(0.8))
                                .multilineTextAlignment(.center)
//                                .lineLimit(3)
                                .frame(
                                    maxWidth: geo.size.width * 0.6,
                                    alignment: .center
                                )
                        

                            // 右箭头
                            Button(action: onNext ) {
                                Image("right_arrow")
                                    .resizable()
                                    .aspectRatio(contentMode: .fit)
                                    .frame(height: 140)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        // 滑动手势控制切换
                        .gesture(
                            DragGesture()
                                .onEnded { value in
                                    // 向右滑动（左滑手势）
                                    if value.translation.width < -50 {
                                        onNext()
                                    }
                                    // 向左滑动（右滑手势）
                                    if value.translation.width > 50 {
                                        onPrevious()
                                    }
                                }
                        )
                    }
                }
            }
        }
        .padding(.vertical, Theme.Spacing.md)
        .background(
            RoundedRectangle(cornerRadius: Theme.CornerRadius.xl)
                .fill(Color.black.opacity(0.4))
                .blur(radius: 10)
        )
        .padding(.horizontal, Theme.Spacing.md)
    }
}

// MARK: - 占位按钮组件
struct PlaceholderButton: View {
    let icon: String

    var body: some View {
        Button(action: {}) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(.white.opacity(0.7))
                .frame(width: 50, height: 50)
                .background(
                    Circle()
                        .fill(Color.black.opacity(0.3))
                )
        }
        .advancedCardButtonStyle()
    }
}

// MARK: - 装扮信息区域
struct PetOutfitSection: View {
    private let container_height: CGFloat = 242
    private let text_height: CGFloat = 52
    
    var body: some View {
        ZStack {
            // 底部轮廓（Rectangle）
            Rectangle()
                .foregroundColor(.clear)
                .frame(height: container_height)
                .frame(maxWidth: .infinity)
                .background(
                    Color(
                        uiColor: UIColor(
                            red: 0.49,
                            green: 0.75,
                            blue: 0.2,
                            alpha: 1.0
                        )
                    )
                )
                .cornerRadius(30)
                .overlay(
                    RoundedRectangle(cornerRadius: 30)
                        .inset(by: 0.25)
                        .stroke(.white.opacity(0.5), lineWidth: 0.5)
                )
                .blur(radius: 0.5)

            // 内容区域（VStack + 半透明背景）
            GeometryReader { geometry in
                VStack(spacing: 0) {
                    // 标题栏（固定高度）
                    HStack {
                        Text("我的装扮")
                            .font(.title2Brand)
                            .foregroundColor(.white)

                        HStack(spacing: 4) {
                            Image(systemName: "square.grid.3x3")
                                .font(.caption)
                                .foregroundColor(.white.opacity(0.8))
                            Text("1/8")
                                .font(.captionBrand)
                                .foregroundColor(.white.opacity(0.8))
                        }
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Capsule().fill(Color.black.opacity(0.4)))
                        
                        Spacer()

                        Button(action: {}) {
                            Image(systemName: "plus")
                                .font(.title3)
                                .foregroundColor(.white)
                                .frame(width: 32, height: 32)
                                .background(Circle().fill(Color.primaryGradient.opacity(0.8)))
                        }
                        .advancedCardButtonStyle()
                    }
                    .padding(.horizontal, Theme.Spacing.lg)
                    .frame(height: text_height) // 固定标题栏高度

                    // ScrollView 填充剩余空间
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: Theme.Spacing.md) {
                            OutfitItemView(imageName: "pet_小碳", title: "礼冠套装", isSelected: true)
                            ForEach(0..<3, id: \.self) { index in
                                OutfitItemView(imageName: nil, title: "花冠套装", isSelected: false)
                            }
                        }
                        .padding(.horizontal, Theme.Spacing.lg)
                    }
                    .frame(height: geometry.size.height - text_height) // 动态计算高度
                    .background(.ultraThinMaterial.opacity(0.8), in: RoundedRectangle(cornerRadius: 30, style: .continuous))
                    .overlay(
                        RoundedRectangle(cornerRadius: 30)
                            .inset(by: 0.5)
                            .stroke(.white.opacity(0.5), lineWidth: 1)
                    )
                }
            }
        }
        .frame(height: container_height)
        }
}

// MARK: - 装扮项目组件
struct OutfitItemView: View {
    let imageName: String?
    let title: String
    let isSelected: Bool

    var body: some View {
        VStack(spacing: Theme.Spacing.sm) {
            // 装扮图片
            ZStack {
                RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                    .fill(isSelected ? Color.brandColor : Color.black.opacity(0.3))
                    .frame(width: 80, height: 80)

                if let imageName = imageName, let image = UIImage(named: imageName) {
                    Image(uiImage: image)
                        .resizable()
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 60, height: 60)
                } else {
                    // 占位图标
                    Image(systemName: "questionmark")
                        .font(.title)
                        .foregroundColor(.white.opacity(0.5))
                }

                // 选中状态指示器
                if isSelected {
                    VStack {
                        HStack {
                            Spacer()
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.white)
                                .background(
                                    Circle()
                                        .fill(Color.green)
                                        .frame(width: 20, height: 20)
                                )
                        }
                        Spacer()
                    }
                    .frame(width: 80, height: 80)
                    .padding(4)
                }
            }

            // 装扮名称
            Text(title)
                .font(.captionBrand)
                .foregroundColor(.white)
                .lineLimit(1)
        }
        .advancedCardButtonStyle()
    }
}

// MARK: - Preview
#Preview {
    let mockTemplate = PetTemplate(
        name: "小碳", 
        imageName: "pet_小碳", 
        rarity: 2,
        introduction: "草系小萌宠，浑身雪白，翠绿耳饰、颈叶、尾斑，小碳的圆眼睛亮闪闪，永远挂着甜甜的笑容，性格活泼又热情，会陪着你用碳币解锁各种互动玩法。 小碳活力满满，跑向宝藏的速度飞快，能够发现更多路上的东西。"
    )
    let mockUserPet = UserPet(templateName: "小碳", level: 5, experience: 250)
    let mockDisplayModel = PetDisplayModel(template: mockTemplate, userPet: mockUserPet)

    return PetDetailView(displayModel: mockDisplayModel)
}
