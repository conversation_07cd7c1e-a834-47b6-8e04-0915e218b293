//
//  EmptyFootprintView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/5.
//

import SwiftUI

struct EmptyFootprintView: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "seal")
                .font(.system(size: 48))
                .foregroundColor(Color.textSecondary)
                .padding(.bottom, Theme.Spacing.sm)

            // 提示文本
            VStack(spacing: Theme.Spacing.xs) {
                Text("暂无记录")
                    .font(.bodyBrand)
                    .foregroundColor(Color.textPrimary)

                Text("前往打卡开始记录您的碳足迹～")
                    .font(.captionBrand)
                    .foregroundColor(Color.textSecondary)
                    .padding()
            }
            .multilineTextAlignment(.center)
        }
        .frame(maxWidth: UIScreen.main.bounds.size.width * 0.7, maxHeight: .infinity)
        .padding()
        .glassCard()
    }
}

#Preview {
    EmptyFootprintView()
}
