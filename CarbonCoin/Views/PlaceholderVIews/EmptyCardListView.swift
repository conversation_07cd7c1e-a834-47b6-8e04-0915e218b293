//
//  EmptyCardListView.swift
//  CarbonCoin
//
//  Created by Augment Agent on 2025-09-07.
//

import SwiftUI

// MARK: - 空卡片列表视图
struct EmptyCardListView: View {
    let filterType: CardFilterType
    
    var body: some View {
        VStack(spacing: Theme.Spacing.lg) {
            // 图标
            Image(systemName: iconName)
                .font(.system(size: 60))
                .foregroundColor(.textSecondary.opacity(0.6))
            
            // 标题
            Text(titleText)
                .font(.title2Brand)
                .foregroundColor(.textPrimary)
                .multilineTextAlignment(.center)
            
            // 描述
            Text(descriptionText)
                .font(.bodyBrand)
                .foregroundColor(.textSecondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, Theme.Spacing.lg)
            
            // 操作提示
            if filterType == .all {
                VStack(spacing: Theme.Spacing.sm) {
                    Text("开始创建您的第一张卡片吧！")
                        .font(.captionBrand)
                        .foregroundColor(.textSecondary)
                    
                    HStack(spacing: Theme.Spacing.sm) {
                        Image(systemName: "camera.fill")
                            .foregroundColor(.brandGreen)
                        Text("前往主体提取页面")
                            .font(.captionBrand)
                            .foregroundColor(.brandGreen)
                    }
                    .padding(.horizontal, Theme.Spacing.md)
                    .padding(.vertical, Theme.Spacing.sm)
                    .background(
                        RoundedRectangle(cornerRadius: Theme.CornerRadius.md)
                            .fill(Color.brandGreen.opacity(0.1))
                    )
                }
                .padding(.top, Theme.Spacing.md)
            }
        }
        .padding(Theme.Spacing.xl)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
    
    // MARK: - 计算属性
    
    private var iconName: String {
        switch filterType {
        case .all:
            return "rectangle.stack"
        case .created:
            return "person.crop.rectangle.stack"
        case .received:
            return "tray.and.arrow.down"
        }
    }
    
    private var titleText: String {
        switch filterType {
        case .all:
            return "暂无卡片"
        case .created:
            return "暂无创建的卡片"
        case .received:
            return "暂无接收的卡片"
        }
    }
    
    private var descriptionText: String {
        switch filterType {
        case .all:
            return "您还没有任何卡片\n快去创建或接收一些有趣的卡片吧"
        case .created:
            return "您还没有创建任何卡片\n通过主体提取功能创建您的专属卡片"
        case .received:
            return "您还没有接收到任何卡片\n与朋友分享卡片，体验更多乐趣"
        }
    }
}

// MARK: - 卡片筛选类型枚举
enum CardFilterType: String, CaseIterable {
    case all = "所有"
    case created = "我创建的"
    case received = "我接收的"
    
    var displayName: String {
        return self.rawValue
    }
}

// MARK: - 预览
struct EmptyCardListView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: Theme.Spacing.lg) {
            EmptyCardListView(filterType: .all)
                .previewDisplayName("所有卡片为空")
            
            EmptyCardListView(filterType: .created)
                .previewDisplayName("创建的卡片为空")
            
            EmptyCardListView(filterType: .received)
                .previewDisplayName("接收的卡片为空")
        }
        .background(Color.globalBackgroundGradient)
        .previewLayout(.sizeThatFits)
    }
}
