//
//  EmptyLogsView.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/7.
//

import SwiftUI

struct EmptyLogsView: View {
    var body: some View {
        VStack(spacing: Theme.Spacing.md) {
            Image(systemName: "chart.line.text.clipboard")
                .font(.system(size: 48))
                .foregroundColor(Color.textSecondary)
                .padding(.bottom, Theme.Spacing.sm)

            // 提示文本
            VStack(spacing: Theme.Spacing.xs) {
                Text("暂无公开日志")
                    .font(.bodyBrand)
                    .foregroundColor(Color.textPrimary)

                Text("在'碳足迹'中分享您的绿色生活动态~")
                    .font(.captionBrand)
                    .foregroundColor(Color.textSecondary)
                    .padding()
            }
            .multilineTextAlignment(.center)
        }
        .frame(maxWidth: UIScreen.main.bounds.size.width * 0.7, maxHeight: .infinity)
        .padding()
        .glassCard()
    }
}

#Preview {
    EmptyLogsView()
}
