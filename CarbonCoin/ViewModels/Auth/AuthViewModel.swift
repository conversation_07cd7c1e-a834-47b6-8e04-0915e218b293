//
//  AuthViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 认证视图模型

/// 认证视图模型，管理登录/注册界面的状态和业务逻辑
@MainActor
class AuthViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 用户ID输入
    @Published var userId: String = ""
    
    /// 密码输入
    @Published var password: String = ""
    
    /// 确认密码输入（注册时使用）
    @Published var confirmPassword: String = ""
    
    /// 密码是否可见
    @Published var isPasswordVisible: Bool = false
    
    /// 确认密码是否可见
    @Published var isConfirmPasswordVisible: Bool = false
    
    /// 是否正在加载
    @Published var isLoading: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 成功信息
    @Published var successMessage: String?
    
    /// 当前认证模式（登录/注册）
    @Published var authMode: AuthMode = .login
    
    /// 表单验证错误
    @Published var validationErrors: [String: String] = [:]
    
    // MARK: - Dependencies
    
    private let authManager: AuthManagerProtocol
    
    // MARK: - Computed Properties
    
    /// 登录按钮是否可用
    var isLoginButtonEnabled: Bool {
        return !isLoading && 
               !userId.isEmpty && 
               !password.isEmpty &&
               validationErrors.isEmpty
    }
    
    /// 注册按钮是否可用
    var isRegisterButtonEnabled: Bool {
        return !isLoading && 
               !userId.isEmpty && 
               !password.isEmpty &&
               !confirmPassword.isEmpty &&
               password == confirmPassword &&
               validationErrors.isEmpty
    }
    
    /// 当前模式的标题
    var modeTitle: String {
        switch authMode {
        case .login:
            return "登录"
        case .register:
            return "注册"
        }
    }
    
    /// 切换模式的提示文本
    var switchModeText: String {
        switch authMode {
        case .login:
            return "还没有账号？"
        case .register:
            return "已有账号？"
        }
    }
    
    /// 切换模式的按钮文本
    var switchModeButtonText: String {
        switch authMode {
        case .login:
            return "立即注册"
        case .register:
            return "立即登录"
        }
    }
    
    // MARK: - Initialization
    
    init(authManager: AuthManagerProtocol? = nil) {
        self.authManager = authManager ?? AuthManager()
        setupValidation()
    }
    
    // MARK: - Public Methods
    
    /// 执行登录
    func login() async {
        guard validateLoginForm() else { return }
        
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        do {
            _ = try await authManager.login(userId: userId, password: password)
            successMessage = "登录成功！"
            clearForm()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    /// 执行注册
    func register() async {
        guard validateRegisterForm() else { return }
        
        isLoading = true
        errorMessage = nil
        successMessage = nil
        
        do {
            _ = try await authManager.register(userId: userId, password: password)
            successMessage = "注册成功！"
            clearForm()
        } catch {
            errorMessage = error.localizedDescription
        }
        
        isLoading = false
    }
    
    /// 切换认证模式
    func switchAuthMode() {
        authMode = authMode == .login ? .register : .login
        clearMessages()
        clearValidationErrors()
    }
    
    /// 切换密码可见性
    func togglePasswordVisibility() {
        isPasswordVisible.toggle()
    }
    
    /// 切换确认密码可见性
    func toggleConfirmPasswordVisibility() {
        isConfirmPasswordVisible.toggle()
    }
    
    /// 清除表单
    func clearForm() {
        userId = ""
        password = ""
        confirmPassword = ""
        isPasswordVisible = false
        isConfirmPasswordVisible = false
        clearMessages()
        clearValidationErrors()
    }
    
    /// 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    /// 清除验证错误
    func clearValidationErrors() {
        validationErrors.removeAll()
    }
    
    // MARK: - Private Methods
    
    /// 设置实时验证
    private func setupValidation() {
        // 监听用户ID变化
        $userId
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] userId in
                self?.validateUserId(userId)
            }
            .store(in: &cancellables)
        
        // 监听密码变化
        $password
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] password in
                self?.validatePassword(password)
            }
            .store(in: &cancellables)
        
        // 监听确认密码变化
        $confirmPassword
            .debounce(for: .milliseconds(300), scheduler: RunLoop.main)
            .sink { [weak self] confirmPassword in
                self?.validateConfirmPassword(confirmPassword)
            }
            .store(in: &cancellables)
    }
    
    /// 验证登录表单
    private func validateLoginForm() -> Bool {
        clearValidationErrors()
        
        let validation = authManager.validateCredentials(userId: userId, password: password)
        if !validation.isValid {
            errorMessage = validation.errorMessage
            return false
        }
        
        return true
    }
    
    /// 验证注册表单
    private func validateRegisterForm() -> Bool {
        clearValidationErrors()
        
        // 基础验证
        let validation = authManager.validateCredentials(userId: userId, password: password)
        if !validation.isValid {
            errorMessage = validation.errorMessage
            return false
        }
        
        // 确认密码验证
        if password != confirmPassword {
            errorMessage = "两次输入的密码不一致"
            return false
        }
        
        return true
    }
    
    /// 验证用户ID
    private func validateUserId(_ userId: String) {
        if userId.isEmpty {
            validationErrors.removeValue(forKey: "userId")
        } else if userId.count < AuthConfig.minUserIdLength {
            validationErrors["userId"] = "用户ID至少需要\(AuthConfig.minUserIdLength)位字符"
        } else {
            validationErrors.removeValue(forKey: "userId")
        }
    }
    
    /// 验证密码
    private func validatePassword(_ password: String) {
        if password.isEmpty {
            validationErrors.removeValue(forKey: "password")
        } else if password.count < AuthConfig.minPasswordLength {
            validationErrors["password"] = "密码至少需要\(AuthConfig.minPasswordLength)位字符"
        } else {
            validationErrors.removeValue(forKey: "password")
        }
    }
    
    /// 验证确认密码
    private func validateConfirmPassword(_ confirmPassword: String) {
        if authMode == .register && !confirmPassword.isEmpty && confirmPassword != password {
            validationErrors["confirmPassword"] = "两次输入的密码不一致"
        } else {
            validationErrors.removeValue(forKey: "confirmPassword")
        }
    }
    
    // MARK: - Private Properties
    
    private var cancellables = Set<AnyCancellable>()
}

// MARK: - 认证模式枚举

/// 认证模式
enum AuthMode {
    case login
    case register
}
