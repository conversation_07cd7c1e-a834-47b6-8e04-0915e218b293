//
//  FriendVIewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/28.
//

import Foundation
import SwiftUI
import Combine

// MARK: - 好友视图模型

/// 好友系统视图模型，管理所有好友相关的API调用和状态
@MainActor
class FriendViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 好友列表数据
    @Published var friendListData: FriendListData?

    /// 搜索到的用户信息
    @Published var searchedUser: UserInfo?

    /// 搜索文本
    @Published var searchText: String = ""

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    // MARK: - Internal Properties (for extension access)

    let baseURL = AuthConfig.baseURL
    let jsonEncoder = JSONEncoder()
    let jsonDecoder = JSONDecoder()
    private var cancellables = Set<AnyCancellable>()

    // MARK: 请求与好友关系

    /// 所有好友（已接受的好友关系）
    var friends: [Friendship] {
        return friendListData?.accepted ?? []
    }

    /// 待处理的好友请求（我收到的）
    var pendingRequests: [Friendship] {
        return friendListData?.pending.filter { $0.type == .received } ?? []
    }

    /// 我发送的待确认请求
    var sentRequests: [Friendship] {
        return friendListData?.pending.filter { $0.type == .sent } ?? []
    }

    /// 被拒绝的请求
    var rejectedRequests: [Friendship] {
        return friendListData?.rejected ?? []
    }

    // MARK: 初始化

    init() {
        setupDateDecoding()
        setupSearchDebounce()
    }

    // MARK: - Public Methods

    /// 获取好友列表
    func fetchFriendList(for userId: String) async {
        isLoading = true
        errorMessage = nil

        do {
            let friendList = try await performFriendListRequest(userId: userId)
            friendListData = friendList
            print("✅ 好友列表获取成功，好友数量: \(friends.count)")
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            print("❌ 获取好友列表失败: \(friendError.localizedDescription)")
        }

        isLoading = false
    }

    /// 搜索用户
    func searchUser(userId: String) async {
        guard !userId.isEmpty else {
            searchedUser = nil
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let user = try await performUserSearchRequest(userId: userId)
            searchedUser = user
            print("✅ 用户搜索成功: \(user.userId)")
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            searchedUser = nil
            print("❌ 用户搜索失败: \(friendError.localizedDescription)")
        }

        isLoading = false
    }

    /// 发送好友请求
    func sendFriendRequest(from userId: String, to friendId: String) async {
        guard userId != friendId else {
            errorMessage = FriendError.cannotAddSelf.localizedDescription
            return
        }

        isLoading = true
        errorMessage = nil
        successMessage = nil

        do {
            _ = try await performSendFriendRequest(userId: userId, friendId: friendId)
            successMessage = "好友请求发送成功"
            print("✅ 好友请求发送成功: \(userId) -> \(friendId)")

            // 刷新好友列表
            await fetchFriendList(for: userId)
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            print("❌ 发送好友请求失败: \(friendError.localizedDescription)")
        }

        isLoading = false
    }

    /// 处理好友请求（接受或拒绝）
    func handleFriendRequest(userId: String, friendId: String, action: HandleFriendRequestRequest.FriendAction) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        do {
            _ = try await performHandleFriendRequest(userId: userId, friendId: friendId, action: action)
            successMessage = action == .accept ? "已接受好友请求" : "已拒绝好友请求"
            print("✅ 好友请求处理成功: \(action.localizedDescription)")

            // 刷新好友列表
            await fetchFriendList(for: userId)
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            print("❌ 处理好友请求失败: \(friendError.localizedDescription)")
        }

        isLoading = false
    }

    /// 删除好友关系
    func deleteFriend(userId: String, friendId: String) async {
        isLoading = true
        errorMessage = nil
        successMessage = nil

        do {
            try await performDeleteFriend(userId: userId, friendId: friendId)
            successMessage = "好友关系已删除"
            print("✅ 好友关系删除成功: \(userId) - \(friendId)")

            // 刷新好友列表
            await fetchFriendList(for: userId)
        } catch {
            let friendError = mapError(error)
            errorMessage = friendError.localizedDescription
            print("❌ 删除好友关系失败: \(friendError.localizedDescription)")
        }

        isLoading = false
    }

    /// 获取用户与特定好友的关系状态
    func getRelationshipState(with friendId: String) -> FriendRelationshipState {
        // 检查是否已是好友
        if friends.contains(where: { $0.friend.userId == friendId }) {
            return .friend
        }

        // 检查是否有待确认的请求
        if let pendingFriendship = friendListData?.pending.first(where: { $0.friend.userId == friendId }) {
            return pendingFriendship.type == .sent ? .pendingSent : .pendingReceived
        }

        // 检查是否被拒绝
        if rejectedRequests.contains(where: { $0.friend.userId == friendId }) {
            return .rejected
        }

        // 不是好友
        return .notFriend
    }

    /// 清除消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }

    /// 清除搜索结果
    func clearSearchResults() {
        searchedUser = nil
        searchText = ""
    }

    // MARK: - Private Methods

    /// 设置日期解码
    private func setupDateDecoding() {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        jsonDecoder.dateDecodingStrategy = .formatted(formatter)

        jsonEncoder.dateEncodingStrategy = .formatted(formatter)
    }

    /// 设置搜索防抖
    private func setupSearchDebounce() {
        $searchText
            .debounce(for: .milliseconds(500), scheduler: RunLoop.main)
            .sink { [weak self] searchText in
                if !searchText.isEmpty {
                    Task {
                        await self?.searchUser(userId: searchText)
                    }
                }
            }
            .store(in: &cancellables)
    }

    /// 执行用户搜索请求
    private func performUserSearchRequest(userId: String) async throws -> UserInfo {
        guard let url = URL(string: baseURL + "users?userId=\(userId)") else {
            throw FriendError.invalidResponse
        }

        print("🔍 搜索用户: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }

        print("📥 用户搜索响应状态码: \(httpResponse.statusCode)")

        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 用户搜索响应内容: \(responseString)")
        }

        guard (200...299).contains(httpResponse.statusCode) else {
            if httpResponse.statusCode == 404 {
                throw FriendError.userNotFound
            }
            throw FriendError.serverError
        }

        let searchResponse = try jsonDecoder.decode(UserSearchResponse.self, from: data)

        if !searchResponse.success {
            throw FriendError.unknown(searchResponse.error ?? "搜索失败")
        }

        guard let userData = searchResponse.data else {
            throw FriendError.userNotFound
        }

        return userData
    }

    /// 执行好友列表请求
    private func performFriendListRequest(userId: String) async throws -> FriendListData {
        guard let url = URL(string: baseURL + "friends?userId=\(userId)") else {
            throw FriendError.invalidResponse
        }

        print("👥 获取好友列表: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "GET"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }

        print("📥 好友列表响应状态码: \(httpResponse.statusCode)")

        guard (200...299).contains(httpResponse.statusCode) else {
            throw FriendError.serverError
        }

        let friendListResponse = try jsonDecoder.decode(FriendListResponse.self, from: data)

        if !friendListResponse.success {
            throw FriendError.unknown(friendListResponse.error ?? "获取好友列表失败")
        }

        guard let friendListData = friendListResponse.data else {
            throw FriendError.invalidResponse
        }

        return friendListData
    }

    /// 执行发送好友请求
    private func performSendFriendRequest(userId: String, friendId: String) async throws -> FriendOperationData {
        guard let url = URL(string: baseURL + "friends") else {
            throw FriendError.invalidResponse
        }

        print("➕ 发送好友请求: \(url.absoluteString)")

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")

        let requestBody = SendFriendRequestRequest(userId: userId, friendId: friendId)
        request.httpBody = try jsonEncoder.encode(requestBody)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }

        print("📥 发送好友请求响应状态码: \(httpResponse.statusCode)")

        guard (200...299).contains(httpResponse.statusCode) else {
            if httpResponse.statusCode == 400 {
                throw FriendError.friendshipAlreadyExists
            }
            throw FriendError.serverError
        }

        let operationResponse = try jsonDecoder.decode(FriendOperationResponse.self, from: data)

        if !operationResponse.success {
            throw FriendError.unknown(operationResponse.error ?? "发送好友请求失败")
        }

        guard let operationData = operationResponse.data else {
            throw FriendError.invalidResponse
        }

        return operationData
    }

    /// 映射错误类型
    private func mapError(_ error: Error) -> FriendError {
        if let friendError = error as? FriendError {
            return friendError
        }

        if error is URLError {
            return .networkError
        }

        return .unknown(error.localizedDescription)
    }
}


// MARK: 网络请求扩展

extension FriendViewModel {
    
    /// 执行处理好友请求
    func performHandleFriendRequest(userId: String, friendId: String, action: HandleFriendRequestRequest.FriendAction) async throws -> FriendOperationData {
        guard let url = URL(string: baseURL + "friends") else {
            throw FriendError.invalidResponse
        }
        
        print("🤝 处理好友请求: \(url.absoluteString), 操作: \(action.localizedDescription)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "PUT"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = HandleFriendRequestRequest(userId: userId, friendId: friendId, action: action)
        request.httpBody = try jsonEncoder.encode(requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }
        
        print("📥 处理好友请求响应状态码: \(httpResponse.statusCode)")
        
        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 处理好友请求响应内容: \(responseString)")
        }
        
        guard (200...299).contains(httpResponse.statusCode) else {
            if httpResponse.statusCode == 404 {
                throw FriendError.friendshipNotFound
            }
            throw FriendError.serverError
        }
        
        let operationResponse = try jsonDecoder.decode(FriendOperationResponse.self, from: data)
        
        if !operationResponse.success {
            throw FriendError.unknown(operationResponse.error ?? "处理好友请求失败")
        }
        
        guard let operationData = operationResponse.data else {
            throw FriendError.invalidResponse
        }
        
        return operationData
    }
    
    /// 执行删除好友请求
    func performDeleteFriend(userId: String, friendId: String) async throws {
        guard let url = URL(string: baseURL + "friends") else {
            throw FriendError.invalidResponse
        }
        
        print("🗑️ 删除好友关系: \(url.absoluteString)")
        
        var request = URLRequest(url: url)
        request.httpMethod = "DELETE"
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        
        let requestBody = DeleteFriendRequest(userId: userId, friendId: friendId)
        request.httpBody = try jsonEncoder.encode(requestBody)
        
        let (data, response) = try await URLSession.shared.data(for: request)
        
        guard let httpResponse = response as? HTTPURLResponse else {
            throw FriendError.networkError
        }
        
        print("📥 删除好友关系响应状态码: \(httpResponse.statusCode)")
        
        if let responseString = String(data: data, encoding: .utf8) {
            print("📥 删除好友关系响应内容: \(responseString)")
        }
        
        guard (200...299).contains(httpResponse.statusCode) else {
            if httpResponse.statusCode == 404 {
                throw FriendError.friendshipNotFound
            }
            throw FriendError.serverError
        }
        
        // 删除操作只需要检查状态码，不需要解析响应数据
        print("✅ 好友关系删除成功")
    }
}
