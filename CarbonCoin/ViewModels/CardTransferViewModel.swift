//
//  CardTransferViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import Foundation
import SwiftUI

/// 卡片传输视图模型，管理卡片传输的所有业务逻辑
@MainActor
class CardTransferViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 接收到的传输记录
    @Published var receivedTransfers: [CardTransferRecord] = []

    /// 发送的传输记录
    @Published var sentTransfers: [CardTransferRecord] = []

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 成功信息
    @Published var successMessage: String?

    /// 是否正在处理传输（创建、接受、拒绝）
    @Published var isProcessingTransfer: Bool = false

    /// 最后更新时间
    @Published var lastUpdateTime: Date?

    // MARK: - Private Properties

    private let cardTransferService: CardTransferServiceProtocol

    /// 获取当前用户ID
    @AppStorage("currentUserId") private var currentUserId: String = ""

    // MARK: - Computed Properties

    /// 当前用户ID（便利访问）
    var userId: String {
        return currentUserId
    }

    /// 待处理的接收传输
    var pendingReceivedTransfers: [CardTransferRecord] {
        return receivedTransfers.filter { $0.status == .pending }
            .sorted { $0.transferTime > $1.transferTime }
    }

    /// 已接受的接收传输
    var acceptedReceivedTransfers: [CardTransferRecord] {
        return receivedTransfers.filter { $0.status == .accepted }
            .sorted { $0.transferTime > $1.transferTime }
    }

    /// 已拒绝的接收传输
    var rejectedReceivedTransfers: [CardTransferRecord] {
        return receivedTransfers.filter { $0.status == .rejected }
            .sorted { $0.transferTime > $1.transferTime }
    }

    /// 待处理的发送传输
    var pendingSentTransfers: [CardTransferRecord] {
        return sentTransfers.filter { $0.status == .pending }
            .sorted { $0.transferTime > $1.transferTime }
    }

    /// 已接受的发送传输
    var acceptedSentTransfers: [CardTransferRecord] {
        return sentTransfers.filter { $0.status == .accepted }
            .sorted { $0.transferTime > $1.transferTime }
    }

    /// 已拒绝的发送传输
    var rejectedSentTransfers: [CardTransferRecord] {
        return sentTransfers.filter { $0.status == .rejected }
            .sorted { $0.transferTime > $1.transferTime }
    }

    // MARK: - Initialization

    init(cardTransferService: CardTransferServiceProtocol = CardTransferService()) {
        self.cardTransferService = cardTransferService
    }

    // MARK: - Public Methods

    /// 创建卡片传输请求（拖拽功能）
    /// - Parameters:
    ///   - cardId: 要传输的卡片ID
    ///   - receiverId: 接收者用户ID
    func createCardTransfer(cardId: String, receiverId: String) async {
        guard !userId.isEmpty else {
            await showError("用户未登录")
            return
        }

        guard !cardId.isEmpty, !receiverId.isEmpty else {
            await showError("参数错误：卡片ID或接收者ID为空")
            return
        }

        isProcessingTransfer = true
        errorMessage = nil

        do {
            let transferRecord = try await cardTransferService.createCardTransfer(
                senderId: userId,
                receiverId: receiverId,
                cardId: cardId
            )

            // 添加到发送列表
            sentTransfers.append(transferRecord)
            lastUpdateTime = Date()

            await showSuccess("卡片传输请求已发送")
            print("✅ 卡片传输创建成功: \(transferRecord.id)")

        } catch {
            await handleError(error, context: "创建卡片传输")
        }

        isProcessingTransfer = false
    }

    /// 处理传输请求（接受或拒绝）
    /// - Parameters:
    ///   - transferId: 传输记录ID
    ///   - action: 操作类型（"accept" 或 "reject"）
    func processCardTransfer(transferId: String, action: String) async {
        guard !userId.isEmpty else {
            await showError("用户未登录")
            return
        }

        guard action == "accept" || action == "reject" else {
            await showError("无效的操作类型")
            return
        }

        isProcessingTransfer = true
        errorMessage = nil

        do {
            let updatedRecord = try await cardTransferService.processCardTransfer(
                transferId: transferId,
                action: action,
                userId: userId
            )

            // 更新接收列表中的记录
            if let index = receivedTransfers.firstIndex(where: { $0.id == transferId }) {
                receivedTransfers[index] = updatedRecord
            }

            lastUpdateTime = Date()

            let actionText = action == "accept" ? "接受" : "拒绝"
            await showSuccess("已\(actionText)卡片传输")
            print("✅ 卡片传输处理成功: \(actionText) - \(transferId)")

        } catch {
            await handleError(error, context: "处理卡片传输")
        }

        isProcessingTransfer = false
    }

    /// 加载接收到的传输记录
    /// - Parameter status: 可选的状态过滤
    func loadReceivedTransfers(status: CardTransferStatus? = nil) async {
        guard !userId.isEmpty else {
            await showError("用户未登录")
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let transfers = try await cardTransferService.getReceivedTransfers(
                userId: userId,
                status: status
            )

            if status == nil {
                // 加载所有状态，替换整个列表
                receivedTransfers = transfers
            } else {
                // 加载特定状态，合并到现有列表
                let existingTransfers = receivedTransfers.filter { $0.status != status }
                receivedTransfers = existingTransfers + transfers
            }

            lastUpdateTime = Date()
            print("✅ 接收传输记录加载成功: \(transfers.count) 条")

        } catch {
            await handleError(error, context: "加载接收传输记录")
        }

        isLoading = false
    }

    /// 加载发送的传输记录
    /// - Parameter status: 可选的状态过滤
    func loadSentTransfers(status: CardTransferStatus? = nil) async {
        guard !userId.isEmpty else {
            await showError("用户未登录")
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let transfers = try await cardTransferService.getSentTransfers(
                userId: userId,
                status: status
            )

            if status == nil {
                // 加载所有状态，替换整个列表
                sentTransfers = transfers
            } else {
                // 加载特定状态，合并到现有列表
                let existingTransfers = sentTransfers.filter { $0.status != status }
                sentTransfers = existingTransfers + transfers
            }

            lastUpdateTime = Date()
            print("✅ 发送传输记录加载成功: \(transfers.count) 条")

        } catch {
            await handleError(error, context: "加载发送传输记录")
        }

        isLoading = false
    }

    /// 刷新所有传输记录
    func refreshAllTransfers() async {
        await loadReceivedTransfers()
        await loadSentTransfers()
    }

    /// 清除错误和成功消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }

    // MARK: - Private Helper Methods

    /// 处理错误
    /// - Parameters:
    ///   - error: 错误对象
    ///   - context: 错误上下文
    private func handleError(_ error: Error, context: String) async {
        let errorText: String

        if let cardTransferError = error as? CardTransferError {
            errorText = cardTransferError.localizedDescription
        } else {
            errorText = error.localizedDescription
        }

        await showError("\(context)失败: \(errorText)")
        print("❌ \(context)失败: \(errorText)")
    }

    /// 显示错误消息
    /// - Parameter message: 错误消息
    private func showError(_ message: String) async {
        await MainActor.run {
            errorMessage = message
            successMessage = nil
        }
    }

    /// 显示成功消息
    /// - Parameter message: 成功消息
    private func showSuccess(_ message: String) async {
        await MainActor.run {
            successMessage = message
            errorMessage = nil
        }
    }

    // MARK: - Convenience Methods

    /// 获取指定传输记录
    /// - Parameter transferId: 传输记录ID
    /// - Returns: 传输记录（如果存在）
    func getTransferRecord(transferId: String) -> CardTransferRecord? {
        return receivedTransfers.first { $0.id == transferId } ??
               sentTransfers.first { $0.id == transferId }
    }

    /// 检查是否有待处理的传输
    var hasPendingTransfers: Bool {
        return !pendingReceivedTransfers.isEmpty
    }

    /// 获取传输统计信息
    var transferStats: (received: Int, sent: Int, pending: Int) {
        return (
            received: receivedTransfers.count,
            sent: sentTransfers.count,
            pending: pendingReceivedTransfers.count
        )
    }

    /// 格式化最后更新时间
    var formattedLastUpdateTime: String {
        guard let lastUpdateTime = lastUpdateTime else {
            return "从未更新"
        }

        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        formatter.locale = Locale(identifier: "zh_CN")

        return "最后更新: \(formatter.string(from: lastUpdateTime))"
    }
}
