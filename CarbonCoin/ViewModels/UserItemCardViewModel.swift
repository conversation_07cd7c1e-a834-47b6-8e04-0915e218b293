//
//  UserItemCardViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation
import SwiftUI

// MARK: - 用户持有卡片的ViewModel
@MainActor
class UserItemCardViewModel: ObservableObject {

    // MARK: - Published Properties
    @Published var userItemCards: [UserItemCard] = []
    @Published var isLoading: Bool = false
    @Published var errorMessage: String?

    // MARK: - Private Properties
    let itemCardManager: ItemCardManager

    // MARK: - Initialization
    init(itemCardManager: ItemCardManager = ItemCardManager()) {
        self.itemCardManager = itemCardManager
    }

    // MARK: - Public Methods

    /// 获取用户持有的所有卡片
    /// - Parameter userId: 用户ID
    func fetchUserItemCards(for userId: String) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let cards = try await itemCardManager.getUserItemCards(userId: userId)
            userItemCards = cards
            print("✅ 成功获取用户持有的卡片: \(cards.count) 张")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 获取用户持有的卡片失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    /// 删除用户持有的卡片
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - cardId: 卡片ID
    func removeUserItemCard(userId: String, cardId: String) async {
        guard !userId.isEmpty, !cardId.isEmpty else {
            errorMessage = "用户ID和卡片ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            try await itemCardManager.deleteUserItemCard(userId: userId, cardId: cardId)

            // 从本地数据中移除
            userItemCards.removeAll { $0.cardId == cardId && $0.userId == userId }

            print("✅ 成功删除用户持有的卡片")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 删除用户持有的卡片失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    /// 获取用户拥有的卡片（作者）
    var ownedCards: [UserItemCard] {
        return userItemCards.filter { $0.isAuthor }
    }

    /// 获取用户接收的卡片（非作者）
    var receivedCards: [UserItemCard] {
        return userItemCards.filter { !$0.isAuthor }
    }

    /// 根据卡片ID查找用户持有的卡片
    /// - Parameter cardId: 卡片ID
    /// - Returns: 用户持有的卡片，如果不存在则返回nil
    func getUserItemCard(by cardId: String) -> UserItemCard? {
        return userItemCards.first { $0.cardId == cardId }
    }
}
