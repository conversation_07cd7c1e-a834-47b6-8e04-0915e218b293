//
//  ItemCardViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/24.
//

import Foundation
import SwiftUI

// MARK: ViewModel
// 可观察的卡片视图模型，用于支持图像编辑等功能
class ItemCardViewModel: ObservableObject, Identifiable {
    @Published var itemCard: ItemCard

    var id: String { itemCard.id }

    init(itemCard: ItemCard) {
        self.itemCard = itemCard
    }

    // 更新图片并保存到文件系统
    func updateImage(_ newImage: UIImage) {
        guard let data = newImage.pngData() else { return }

        // 使用原有的文件名或生成新的文件名（确保使用 PNG 扩展名）
        let fileName: String
        if itemCard.imageFileName.isEmpty {
            fileName = "\(itemCard.id).png"
        } else {
            // 如果原文件名是 .jpg，改为 .png
            let baseName = itemCard.imageFileName.replacingOccurrences(of: ".jpg", with: "").replacingOccurrences(of: ".png", with: "")
            fileName = "\(baseName).png"
        }

        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first!.appendingPathComponent(fileName)

        do {
            try data.write(to: url)
            // 更新模型，包含新的字段
            itemCard = ItemCard(
                id: itemCard.id,
                cardType: itemCard.cardType,
                themeColor: itemCard.themeColor,
                coinReward: itemCard.coinReward,
                experienceReward: itemCard.experienceReward,
                description: itemCard.description,
                title: itemCard.title,
                imageFileName: fileName,
                imageURL: itemCard.imageURL,
                createdAt: itemCard.createdAt,
                authorId: itemCard.authorId,
                location: itemCard.location,
                latitude: itemCard.latitude,
                longitude: itemCard.longitude
            )
        } catch {
            print("保存图片失败: \(error)")
        }
    }

    // 注意：个性化备注功能已移除，后端不再支持用户独立备注

    // 更新位置信息
    func updateLocation(_ newLocation: String) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: itemCard.cardType,
            themeColor: itemCard.themeColor,
            coinReward: itemCard.coinReward,
            experienceReward: itemCard.experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: itemCard.imageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: newLocation,
            latitude: itemCard.latitude,
            longitude: itemCard.longitude
        )
    }

    // 更新经纬度信息
    func updateCoordinates(latitude: Double?, longitude: Double?) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: itemCard.cardType,
            themeColor: itemCard.themeColor,
            coinReward: itemCard.coinReward,
            experienceReward: itemCard.experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: itemCard.imageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: itemCard.location,
            latitude: latitude,
            longitude: longitude
        )
    }

    // 更新图片URL（用于图床功能）
    func updateImageURL(_ newImageURL: String) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: itemCard.cardType,
            themeColor: itemCard.themeColor,
            coinReward: itemCard.coinReward,
            experienceReward: itemCard.experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: newImageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: itemCard.location,
            latitude: itemCard.latitude,
            longitude: itemCard.longitude
        )
    }

    // 更新卡片类型
    func updateCardType(_ newCardType: CardType) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: newCardType,
            themeColor: itemCard.themeColor,
            coinReward: itemCard.coinReward,
            experienceReward: itemCard.experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: itemCard.imageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: itemCard.location,
            latitude: itemCard.latitude,
            longitude: itemCard.longitude
        )
    }

    // 更新主题色
    func updateThemeColor(_ newThemeColor: String?) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: itemCard.cardType,
            themeColor: newThemeColor,
            coinReward: itemCard.coinReward,
            experienceReward: itemCard.experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: itemCard.imageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: itemCard.location,
            latitude: itemCard.latitude,
            longitude: itemCard.longitude
        )
    }

    // 更新奖励信息
    func updateRewards(coinReward: Int, experienceReward: Int) {
        itemCard = ItemCard(
            id: itemCard.id,
            cardType: itemCard.cardType,
            themeColor: itemCard.themeColor,
            coinReward: coinReward,
            experienceReward: experienceReward,
            description: itemCard.description,
            title: itemCard.title,
            imageFileName: itemCard.imageFileName,
            imageURL: itemCard.imageURL,
            createdAt: itemCard.createdAt,
            authorId: itemCard.authorId,
            location: itemCard.location,
            latitude: itemCard.latitude,
            longitude: itemCard.longitude
        )
    }

    // 删除卡片图像文件
    func deleteImageFile() {
        guard !itemCard.imageFileName.isEmpty else { return }

        let url = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
            .first!.appendingPathComponent(itemCard.imageFileName)

        do {
            try FileManager.default.removeItem(at: url)
            print("成功删除图像文件: \(itemCard.imageFileName)")
        } catch {
            print("删除图像文件失败: \(error)")
        }
    }
}



