//
//  LocationViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/30.
//

import Foundation
import CoreLocation
import Combine

// MARK: - 位置视图模型

/// 位置视图模型，管理用户位置的查询、更新和本地历史记录
@MainActor
class LocationViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 当前用户位置信息
    @Published var currentUserLocation: UserLocationInfo?

    /// 位置历史记录（最近10个位置）
    @Published var locationHistory: [LocationHistoryItem] = []

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 是否正在自动更新位置
    @Published var isAutoUpdating: Bool = false

    /// 最后更新时间
    @Published var lastUpdateTime: Date?
    
    let userLocationManager: UserLocationManager

    // MARK: - Private Properties

    private let locationUpdater: LocationUpdaterProtocol
    
    private var updateTimer: Timer?
    private var cancellables = Set<AnyCancellable>()

    // 本地存储键
    private let locationHistoryKey = "location_history_v1"

    // MARK: - Initialization

    init(locationUpdater: LocationUpdaterProtocol? = nil,
         userLocationManager: UserLocationManager? = nil) {
        // 在主线程上创建默认实例
        self.locationUpdater = locationUpdater ?? LocationUpdater()
        self.userLocationManager = userLocationManager ?? UserLocationManager()

        loadLocationHistory()
        setupLocationUpdates()
    }

    // MARK: - Public Methods

    /// 开始自动位置更新
    /// - Parameter userId: 用户ID
    func startAutoLocationUpdates(for userId: String) {
        print("📍 startAutoLocationUpdates 被调用，用户ID: \(userId)")

        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            print("❌ 用户ID为空")
            return
        }

        guard !isAutoUpdating else {
            print("📍 位置自动更新已在运行中")
            return
        }

        isAutoUpdating = true
        errorMessage = nil

        print("📍 开始自动位置更新，用户ID: \(userId)")
        print("📍 更新间隔: \(LocationUpdateConfig.updateInterval)秒")
        print("📍 精度阈值: \(LocationUpdateConfig.accuracyThreshold)米")
        print("📍 最小距离: \(LocationUpdateConfig.minimumDistance)米")

        // 立即执行一次位置更新
        Task {
            print("📍 执行首次位置更新...")
            await updateLocationOnce(for: userId)
        }

        // 设置定时器，每5秒更新一次
        updateTimer = Timer.scheduledTimer(withTimeInterval: LocationUpdateConfig.updateInterval, repeats: true) { [weak self] timer in
            print("📍 定时器触发，准备更新位置...")
            Task { @MainActor in
                guard let self = self else {
                    timer.invalidate()
                    return
                }
                await self.updateLocationOnce(for: userId)
            }
        }

        // 确保定时器在主运行循环中运行
        if let timer = updateTimer {
            RunLoop.main.add(timer, forMode: .common)
            print("📍 定时器已添加到主运行循环")
        }
    }

    /// 停止自动位置更新
    func stopAutoLocationUpdates() {
        guard isAutoUpdating else {
            return
        }

        isAutoUpdating = false
        updateTimer?.invalidate()
        updateTimer = nil

        print("📍 停止自动位置更新")
    }

    /// 手动查询用户位置
    /// - Parameter userId: 用户ID
    func queryUserLocation(for userId: String) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            let locationInfo = try await locationUpdater.queryUserLocation(userId: userId)
            currentUserLocation = locationInfo
            print("✅ 成功查询用户位置")
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 查询用户位置失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    /// 手动更新用户位置
    /// - Parameter userId: 用户ID
    func updateUserLocation(for userId: String) async {
        await updateLocationOnce(for: userId)
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    /// 清除位置历史记录
    func clearLocationHistory() {
        locationHistory.removeAll()
        saveLocationHistory()
    }

    // MARK: - Private Methods

    /// 设置位置更新监听
    private func setupLocationUpdates() {
        // 监听位置管理器的位置更新
        userLocationManager.$currentLocation
            .compactMap { $0 }
            .sink { [weak self] location in
                // 这里可以根据需要处理位置更新
                print("📍 位置管理器位置更新: \(location.coordinate)")
            }
            .store(in: &cancellables)

        // 确保位置管理器开始更新位置
        print("📍 请求位置权限并开始位置更新")
        userLocationManager.requestLocationPermission()
    }

    /// 执行一次位置更新
    /// - Parameter userId: 用户ID
    private func updateLocationOnce(for userId: String) async {
        print("📍 开始执行位置更新，用户ID: \(userId)")

        // 获取当前位置
        let (location, locationString) = await userLocationManager.getCurrentLocationInfo()
        print("📍 获取位置结果: location=\(location?.coordinate ?? nil), string=\(locationString)")

        guard let currentLocation = location else {
            errorMessage = "无法获取当前位置: \(locationString)"
            print("❌ 无法获取当前位置: \(locationString)")
            return
        }

        print("📍 当前位置: \(currentLocation.coordinate.latitude), \(currentLocation.coordinate.longitude)")
        print("📍 位置精度: \(currentLocation.horizontalAccuracy)m")

        // 检查位置精度
        guard currentLocation.horizontalAccuracy < LocationUpdateConfig.accuracyThreshold else {
            print("📍 位置精度不足，跳过更新: \(currentLocation.horizontalAccuracy)m (阈值: \(LocationUpdateConfig.accuracyThreshold)m)")
            return
        }

        // 检查是否需要更新（距离上次位置是否足够远）
        if let lastLocation = currentUserLocation?.clLocation {
            let distance = currentLocation.distance(from: lastLocation)
            print("📍 距离上次位置: \(distance)m (最小距离: \(LocationUpdateConfig.minimumDistance)m)")
            if distance < LocationUpdateConfig.minimumDistance {
                print("📍 移动距离不足，跳过更新: \(distance)m")
                return
            }
        } else {
            print("📍 首次位置更新，无需检查距离")
        }

        print("📍 准备调用服务器更新位置...")

        do {
            // 更新到服务器
            let success = try await locationUpdater.updateUserLocation(userId: userId, location: currentLocation)
            print("📍 服务器更新结果: \(success)")

            if success {
                // 创建位置信息
                let userLocationInfo = UserLocationInfo(
                    userId: userId,
                    location: currentLocation,
                    locationString: locationString
                )

                // 更新当前位置
                currentUserLocation = userLocationInfo
                lastUpdateTime = Date()

                // 添加到历史记录
                addToLocationHistory(userLocationInfo)

                print("✅ 位置更新成功: \(currentLocation.coordinate)")
            } else {
                print("❌ 服务器返回更新失败")
            }
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 位置更新失败: \(error.localizedDescription)")
        }
    }

    /// 添加位置到历史记录
    /// - Parameter userLocationInfo: 用户位置信息
    private func addToLocationHistory(_ userLocationInfo: UserLocationInfo) {
        let historyItem = LocationHistoryItem(userLocationInfo: userLocationInfo)

        // 添加到数组开头
        locationHistory.insert(historyItem, at: 0)

        // 保持最多10个记录
        if locationHistory.count > LocationUpdateConfig.maxHistoryCount {
            locationHistory = Array(locationHistory.prefix(LocationUpdateConfig.maxHistoryCount))
        }

        // 保存到本地存储
        saveLocationHistory()

        print("📍 添加位置历史记录，当前记录数: \(locationHistory.count)")
    }

    /// 加载位置历史记录
    private func loadLocationHistory() {
        guard let data = UserDefaults.standard.data(forKey: locationHistoryKey),
              let history = try? JSONDecoder().decode([LocationHistoryItem].self, from: data) else {
            print("📍 没有找到位置历史记录")
            return
        }

        locationHistory = history
        print("📍 加载位置历史记录: \(locationHistory.count) 条")
    }

    /// 保存位置历史记录
    private func saveLocationHistory() {
        guard let data = try? JSONEncoder().encode(locationHistory) else {
            print("❌ 保存位置历史记录失败")
            return
        }

        UserDefaults.standard.set(data, forKey: locationHistoryKey)
        print("📍 保存位置历史记录: \(locationHistory.count) 条")
    }
}

// MARK: - Deinitializer

extension LocationViewModel {
    /// 清理资源
    func cleanup() {
        stopAutoLocationUpdates()
        cancellables.removeAll()
    }
}
