//
//  FootprintsViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/9/2.
//

import Foundation
import CoreLocation
import MapKit
import Combine

// MARK: - 足迹视图模型
// 定义更新足迹的超参数、实现本地计时

/// 足迹视图模型，管理出行打卡功能的状态和业务逻辑
@MainActor
class FootprintsViewModel: ObservableObject {

    // MARK: - Published Properties

    /// 当前足迹记录列表
    @Published var footprintsList: [UserFootprints] = []

    /// 当前正在进行的足迹记录
    @Published var currentFootprints: UserFootprints?

    /// 是否正在打卡中
    @Published var isTracking: Bool = false

    /// 当前选择的出行方式
    @Published var selectedActivityType: ActivityType = .walking

    /// 轨迹坐标数组（用于MapKit绘制）
    @Published var trackingCoordinates: [CLLocationCoordinate2D] = []

    /// 是否正在加载
    @Published var isLoading: Bool = false

    /// 错误信息
    @Published var errorMessage: String?

    /// 是否向后端发送API请求
    @Published var shouldSendAPIRequests: Bool = true

    /// 足迹统计信息
    @Published var footprintsStats: FootprintsStatsResponse?

    /// 最后更新时间
    @Published var lastUpdateTime: Date?

    /// 最后完成的足迹记录（用于显示打卡结果）
    @Published var lastCompletedFootprints: UserFootprints?

    /// 本地实时计时显示（仅用于UI显示，不影响后端数据）
    @Published var localTrackingDuration: TimeInterval = 0

    /// 打卡开始时间（本地记录，用于实时计时）
    private var trackingStartTime: Date?

    // MARK: - Private Properties

    private let footprintsManager: FootprintsManagerProtocol
    private let locationViewModel: LocationViewModel
    private var trackingTimer: Timer?
    private var displayTimer: Timer? // 新增：用于实时更新显示时间的定时器
    private var cancellables = Set<AnyCancellable>()

    // 位置更新配置
    private let trackingInterval: TimeInterval = 10.0  // 30秒更新一次
    private let minimumDistance: Double = 5.0         // 最小移动距离10米

    // MARK: - Initialization

    init(footprintsManager: FootprintsManagerProtocol? = nil,
         locationViewModel: LocationViewModel? = nil) {
        self.footprintsManager = footprintsManager ?? FootprintsManager()
        self.locationViewModel = locationViewModel ?? LocationViewModel()

        setupLocationUpdates()
    }


    // MARK: 开始出行打卡
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - activityType: 出行方式
    func startTracking(userId: String, activityType: ActivityType) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        guard !isTracking else {
            print("📍 出行打卡已在进行中")
            return
        }

        print("🚶 开始出行打卡，用户ID: \(userId), 活动类型: \(activityType.displayName)")

        selectedActivityType = activityType
        isTracking = true
        isLoading = true
        errorMessage = nil

        do {
            // 获取当前位置作为起始点
            let (location, _) = await locationViewModel.userLocationManager.getCurrentLocationInfo()

            var initialFootprints: [FootprintPoint] = []
            if let currentLocation = location {
                let footprintPoint = FootprintPoint(location: currentLocation)
                initialFootprints.append(footprintPoint)

                // 更新轨迹坐标
                trackingCoordinates = [currentLocation.coordinate]
                print("📍 添加起始位置: \(currentLocation.coordinate)")
            }

            // 创建足迹记录
            if shouldSendAPIRequests {
                let footprints = try await footprintsManager.createFootprints(
                    userId: userId,
                    activityType: activityType,
                    isFinished: false,
                    footPrints: initialFootprints
                )
                currentFootprints = footprints
                print("✅ 成功创建足迹记录: \(footprints.id)")
            } else {
                // 创建本地足迹记录（用于测试）
                let localFootprints = UserFootprints(
                    id: UUID().uuidString,
                    userId: userId,
                    footPrints: initialFootprints,
                    activityType: activityType,
                    isFinished: false,
                    totalDistance: 0.0,
                    createdAt: Date(),
                    updatedAt: Date()
                )
                currentFootprints = localFootprints
                print("📱 创建本地足迹记录")
            }

            // 开始定时位置更新
            startLocationTracking(userId: userId)

            // 开始本地实时计时
            startLocalDurationTimer()

            isTracking = true
            lastUpdateTime = Date()

        } catch {
            errorMessage = error.localizedDescription
            print("❌ 开始出行打卡失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 结束出行打卡
    func stopTracking() async {
        guard isTracking, let currentFootprints = currentFootprints else {
            return
        }

        print("🚶 结束出行打卡，足迹ID: \(currentFootprints.id)")

        isLoading = true
        errorMessage = nil

        // 停止定时器
        stopLocationTracking()

        // 停止本地实时计时
        stopLocalDurationTimer()

        do {
            // 更新足迹记录为已完成
            if shouldSendAPIRequests {
                let updatedFootprints = try await footprintsManager.updateFootprints(
                    footprintId: currentFootprints.id,
                    footPrints: nil,
                    activityType: nil,
                    isFinished: true
                )

                // 保存最后完成的足迹记录（用于显示打卡结果）
                lastCompletedFootprints = updatedFootprints

                // 更新本地记录
                if let index = footprintsList.firstIndex(where: { $0.id == updatedFootprints.id }) {
                    footprintsList[index] = updatedFootprints
                } else {
                    footprintsList.insert(updatedFootprints, at: 0)
                }

                print("✅ 成功结束足迹记录: \(updatedFootprints.id)")
                print("📊 总距离: \(updatedFootprints.formattedDistance)")
                print("⏱️ 总时长: \(updatedFootprints.formattedDuration)")
            } else {
                // 更新本地记录
                var updatedFootprints = currentFootprints
                updatedFootprints = UserFootprints(
                    id: updatedFootprints.id,
                    userId: updatedFootprints.userId,
                    footPrints: updatedFootprints.footPrints,
                    activityType: updatedFootprints.activityType,
                    isFinished: true,
                    totalDistance: updatedFootprints.totalDistance,
                    createdAt: updatedFootprints.createdAt,
                    updatedAt: Date()
                )

                // 保存最后完成的足迹记录（用于显示打卡结果）
                lastCompletedFootprints = updatedFootprints

                if let index = footprintsList.firstIndex(where: { $0.id == updatedFootprints.id }) {
                    footprintsList[index] = updatedFootprints
                } else {
                    footprintsList.insert(updatedFootprints, at: 0)
                }

                print("📱 本地结束足迹记录")
            }

        } catch {
            errorMessage = error.localizedDescription
            print("❌ 结束出行打卡失败: \(error.localizedDescription)")
        }

        // 重置状态
        isTracking = false
        self.currentFootprints = nil
        trackingCoordinates.removeAll()
        lastUpdateTime = Date()

        isLoading = false
    }

    // MARK: 查询足迹记录列表
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    ///   - activityType: 活动类型
    func loadFootprintsList(userId: String, startDate: Date? = nil, endDate: Date? = nil, activityType: ActivityType? = nil) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            if shouldSendAPIRequests {
                let footprints = try await footprintsManager.queryFootprints(
                    userId: userId,
                    startDate: startDate,
                    endDate: endDate,
                    activityType: activityType
                )
                footprintsList = footprints
                print("✅ 成功加载足迹记录: \(footprints.count) 条")
            } else {
                // 模拟数据（用于测试）
                footprintsList = []
                print("📱 使用模拟数据")
            }
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 加载足迹记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 查询足迹统计
    /// - Parameters:
    ///   - userId: 用户ID
    ///   - startDate: 开始时间
    ///   - endDate: 结束时间
    ///   - activityType: 活动类型
    func loadFootprintsStats(userId: String, startDate: Date? = nil, endDate: Date? = nil, activityType: ActivityType? = nil) async {
        guard !userId.isEmpty else {
            errorMessage = "用户ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            if shouldSendAPIRequests {
                let stats = try await footprintsManager.queryFootprintsStats(
                    userId: userId,
                    startDate: startDate,
                    endDate: endDate,
                    activityType: activityType
                )
                footprintsStats = stats
                print("✅ 成功加载足迹统计")
            } else {
                // 模拟统计数据
                footprintsStats = nil
                print("📱 使用模拟统计数据")
            }
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 加载足迹统计失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 删除足迹记录
    /// - Parameter footprintId: 足迹记录ID
    func deleteFootprints(footprintId: String) async {
        guard !footprintId.isEmpty else {
            errorMessage = "足迹记录ID不能为空"
            return
        }

        isLoading = true
        errorMessage = nil

        do {
            if shouldSendAPIRequests {
                let success = try await footprintsManager.deleteFootprints(footprintId: footprintId)
                if success {
                    // 从本地列表中移除
                    footprintsList.removeAll { $0.id == footprintId }
                    print("✅ 成功删除足迹记录: \(footprintId)")
                }
            } else {
                // 本地删除
                footprintsList.removeAll { $0.id == footprintId }
                print("📱 本地删除足迹记录")
            }
        } catch {
            errorMessage = error.localizedDescription
            print("❌ 删除足迹记录失败: \(error.localizedDescription)")
        }

        isLoading = false
    }

    // MARK: 切换API请求状态
    /// - Parameter enabled: 是否启用API请求
    func toggleAPIRequests(_ enabled: Bool) {
        shouldSendAPIRequests = enabled
        print("🔄 API请求状态: \(enabled ? "启用" : "禁用")")
    }

    /// 清除错误信息
    func clearError() {
        errorMessage = nil
    }

    /// 获取轨迹折线（用于MapKit）
    func getTrackingPolyline() -> MKPolyline? {
        guard !trackingCoordinates.isEmpty else { return nil }
        return MKPolyline(coordinates: trackingCoordinates, count: trackingCoordinates.count)
    }

    /// 获取足迹记录的轨迹折线
    /// - Parameter footprints: 足迹记录
    /// - Returns: 轨迹折线
    func getFootprintsPolyline(for footprints: UserFootprints) -> MKPolyline? {
        let coordinates = footprints.footPrints.map { $0.coordinate }
        guard !coordinates.isEmpty else { return nil }
        return MKPolyline(coordinates: coordinates, count: coordinates.count)
    }

    // MARK: - Private Methods

    /// 设置位置更新监听
    private func setupLocationUpdates() {
        // 监听位置管理器的位置更新
        locationViewModel.$currentUserLocation
            .compactMap { $0 }
            .sink { [weak self] userLocationInfo in
                // 这里可以根据需要处理位置更新
                print("📍 位置更新: \(userLocationInfo.clLocation.coordinate)")
            }
            .store(in: &cancellables)
    }

    /// 开始位置追踪
    /// - Parameter userId: 用户ID
    private func startLocationTracking(userId: String) {
        print("📍 开始位置追踪，间隔: \(trackingInterval)秒")

        // 设置定时器，定期获取位置并更新足迹
        trackingTimer = Timer.scheduledTimer(withTimeInterval: trackingInterval, repeats: true) { [weak self] timer in
            Task { @MainActor in
                guard let self = self else {
                    timer.invalidate()
                    return
                }
                await self.updateTrackingLocation(userId: userId)
            }
        }

        // 确保定时器在主运行循环中运行
        if let timer = trackingTimer {
            RunLoop.main.add(timer, forMode: .common)
            print("📍 位置追踪定时器已启动")
        }
    }

    /// 停止位置追踪
    private func stopLocationTracking() {
        trackingTimer?.invalidate()
        trackingTimer = nil
        print("📍 位置追踪定时器已停止")
    }

    // MARK: - 本地实时计时功能

    /// 开始本地实时计时（每秒更新一次显示时间）
    private func startLocalDurationTimer() {
        // 记录开始时间
        trackingStartTime = Date()
        localTrackingDuration = 0

        print("⏱️ 开始本地实时计时")

        // 设置每秒更新一次的定时器
        displayTimer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] timer in
            Task { @MainActor in
                guard let self = self else {
                    timer.invalidate()
                    return
                }
                self.updateLocalDuration()
            }
        }

        // 确保定时器在主运行循环中运行
        if let timer = displayTimer {
            RunLoop.main.add(timer, forMode: .common)
        }
    }

    /// 停止本地实时计时
    private func stopLocalDurationTimer() {
        displayTimer?.invalidate()
        displayTimer = nil
        trackingStartTime = nil
        print("⏱️ 本地实时计时已停止")
    }

    /// 更新本地显示时长
    private func updateLocalDuration() {
        guard let startTime = trackingStartTime else { return }

        let currentTime = Date()
        localTrackingDuration = currentTime.timeIntervalSince(startTime)

        // 可选：输出调试信息（每10秒输出一次，避免日志过多）
        let seconds = Int(localTrackingDuration)
        if seconds % 10 == 0 && seconds > 0 {
            print("⏱️ 本地计时更新: \(formatDuration(localTrackingDuration))")
        }
    }

    /// 格式化时长显示（本地计时专用）
    /// - Parameter duration: 时长（秒）
    /// - Returns: 格式化的时长字符串
    private func formatDuration(_ duration: TimeInterval) -> String {
        let totalSeconds = Int(duration)
        let hours = totalSeconds / 3600
        let minutes = (totalSeconds % 3600) / 60
        let seconds = totalSeconds % 60

        if hours > 0 {
            return String(format: "%d小时%d分钟%d秒", hours, minutes, seconds)
        } else if minutes > 0 {
            return String(format: "%d分钟%d秒", minutes, seconds)
        } else {
            return String(format: "%d秒", seconds)
        }
    }

    /// 获取格式化的本地计时时长（供UI使用）
    var formattedLocalDuration: String {
        return formatDuration(localTrackingDuration)
    }

    /// 更新追踪位置
    /// - Parameter userId: 用户ID
    private func updateTrackingLocation(userId: String) async {
        guard isTracking, let currentFootprints = currentFootprints else {
            return
        }

        print("📍 更新追踪位置...")

        // 获取当前位置
        let (location, _) = await locationViewModel.userLocationManager.getCurrentLocationInfo()

        guard let currentLocation = location else {
            print("❌ 无法获取当前位置")
            return
        }

        // 检查位置精度
        guard currentLocation.horizontalAccuracy < 100.0 else {
            print("📍 位置精度不足，跳过更新: \(currentLocation.horizontalAccuracy)m")
            return
        }

        // 检查是否需要更新（距离上次位置是否足够远）
        if let lastCoordinate = trackingCoordinates.last {
            let lastLocation = CLLocation(latitude: lastCoordinate.latitude, longitude: lastCoordinate.longitude)
            let distance = currentLocation.distance(from: lastLocation)

            if distance < minimumDistance {
                print("📍 移动距离不足，跳过更新: \(distance)m")
                return
            }
        }

        print("📍 添加新的足迹点: \(currentLocation.coordinate)")

        // 添加到轨迹坐标
        trackingCoordinates.append(currentLocation.coordinate)

        // 创建足迹点
        let footprintPoint = FootprintPoint(location: currentLocation)

        do {
            // 更新足迹记录
            if shouldSendAPIRequests {
                let updatedFootprints = try await footprintsManager.updateFootprints(
                    footprintId: currentFootprints.id,
                    footPrints: [footprintPoint],
                    activityType: nil,
                    isFinished: nil
                )

                // 更新当前足迹记录
                self.currentFootprints = updatedFootprints
                lastUpdateTime = Date()

                print("✅ 成功更新足迹点，总距离: \(updatedFootprints.formattedDistance)")
            } else {
                // 本地更新
                var updatedFootprints = currentFootprints
                var newFootprints = updatedFootprints.footPrints
                newFootprints.append(footprintPoint)

                updatedFootprints = UserFootprints(
                    id: updatedFootprints.id,
                    userId: updatedFootprints.userId,
                    footPrints: newFootprints,
                    activityType: updatedFootprints.activityType,
                    isFinished: updatedFootprints.isFinished,
                    totalDistance: calculateLocalDistance(footprints: newFootprints),
                    createdAt: updatedFootprints.createdAt,
                    updatedAt: Date()
                )

                self.currentFootprints = updatedFootprints
                lastUpdateTime = Date()

                print("📱 本地更新足迹点")
            }

        } catch {
            print("❌ 更新足迹点失败: \(error.localizedDescription)")
            // 不设置错误信息，避免中断追踪
        }
    }

    // MARK: 计算本地距离
    /// - Parameter footprints: 足迹点数组
    /// - Returns: 总距离（公里）
    private func calculateLocalDistance(footprints: [FootprintPoint]) -> Double {
        guard footprints.count > 1 else { return 0.0 }

        var totalDistance: Double = 0.0

        for i in 1..<footprints.count {
            let previousLocation = footprints[i-1].clLocation
            let currentLocation = footprints[i].clLocation
            let distance = currentLocation.distance(from: previousLocation)
            totalDistance += distance
        }

        return totalDistance / 1000.0 // 转换为公里
    }
}

// MARK: - Deinitializer

extension FootprintsViewModel {
    /// 清理资源
    func cleanup() {
        stopLocationTracking()
        cancellables.removeAll()
    }
}
