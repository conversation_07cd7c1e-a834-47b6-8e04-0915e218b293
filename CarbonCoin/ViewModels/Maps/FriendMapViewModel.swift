//
//  FriendMapViewModel.swift
//  CarbonCoin
//
//  Created by <PERSON><PERSON> on 2025/8/31.
//

import Foundation
import SwiftUI
import MapKit
import Combine

// MARK: - 好友地图标注模型

/// 好友地图标注信息，用于在地图上显示好友位置
struct FriendMapAnnotation: Identifiable, Equatable {
    let id = UUID()
    let friendLocation: FriendLocationInfo
    
    /// 地图坐标
    var coordinate: CLLocationCoordinate2D {
        CLLocationCoordinate2D(
            latitude: friendLocation.latitude,
            longitude: friendLocation.longitude
        )
    }
    
    /// 标注标题
    var title: String {
        friendLocation.nickname
    }
    
    /// 标注副标题
    var subtitle: String {
        friendLocation.isOnline ? "在线" : "离线"
    }
}

// MARK: - 好友地图视图模型

/// 好友地图视图模型，协调好友列表和位置信息的获取，为地图提供标注数据
@MainActor
class FriendMapViewModel: ObservableObject {
    
    // MARK: - Published Properties
    
    /// 好友地图标注数组
    @Published var friendAnnotations: [FriendMapAnnotation] = []
    
    /// 是否正在加载好友位置信息
    @Published var isLoadingFriendLocations: Bool = false
    
    /// 错误信息
    @Published var errorMessage: String?
    
    /// 成功信息
    @Published var successMessage: String?
    
    /// 最后更新时间
    @Published var lastUpdateTime: Date?
    
    // MARK: - Dependencies
    
    private let friendViewModel: FriendViewModel
    private let friendsLocationManager: FriendsLocationManager
    private var cancellables = Set<AnyCancellable>()
    
    // MARK: - Initialization
    
    /// 初始化好友地图视图模型
    /// - Parameters:
    ///   - friendViewModel: 好友视图模型实例（可选，如果不提供将创建新实例）
    ///   - friendsLocationManager: 好友位置管理器实例（可选，如果不提供将创建新实例）
    @MainActor
    init(
        friendViewModel: FriendViewModel? = nil,
        friendsLocationManager: FriendsLocationManager? = nil
    ) {
        self.friendViewModel = friendViewModel ?? FriendViewModel()
        self.friendsLocationManager = friendsLocationManager ?? FriendsLocationManager()

        setupObservers()
    }
    
    // MARK: - Public Methods
    
    /// 加载好友位置信息并更新地图标注
    /// - Parameter userId: 当前用户ID
    func loadFriendLocations(for userId: String) async {
        print("🗺️ 开始加载好友位置信息，用户ID: \(userId)")
        
        isLoadingFriendLocations = true
        errorMessage = nil
        successMessage = nil
        
        do {
            // 1. 首先获取好友列表
            await friendViewModel.fetchFriendList(for: userId)
            
            // 2. 检查是否有好友
            let friends = friendViewModel.friends
            guard !friends.isEmpty else {
                print("🗺️ 用户没有好友，跳过位置查询")
                friendAnnotations = []
                successMessage = "暂无好友位置信息"
                isLoadingFriendLocations = false
                return
            }
            
            // 3. 提取好友ID数组
            let friendIds = friends.map { $0.friend.userId }
            print("🗺️ 准备查询 \(friendIds.count) 个好友的位置信息")
            
            // 4. 批量获取好友位置信息
            let friendLocations = try await friendsLocationManager.fetchFriendsLocation(friendIds: friendIds)
            
            // 5. 转换为地图标注
            let annotations = friendLocations.map { FriendMapAnnotation(friendLocation: $0) }
            
            // 6. 更新UI
            friendAnnotations = annotations
            lastUpdateTime = Date()
            successMessage = "成功加载 \(annotations.count) 个好友的位置信息"
            
            print("✅ 好友位置加载完成，共 \(annotations.count) 个标注")
            
        } catch {
            let errorMsg = "加载好友位置失败: \(error.localizedDescription)"
            errorMessage = errorMsg
            print("❌ \(errorMsg)")
        }
        
        isLoadingFriendLocations = false
    }
    
    /// 刷新好友位置信息
    /// - Parameter userId: 当前用户ID
    func refreshFriendLocations(for userId: String) async {
        print("🔄 刷新好友位置信息")
        await loadFriendLocations(for: userId)
    }
    
    /// 获取指定好友的位置信息
    /// - Parameter friendId: 好友用户ID
    /// - Returns: 好友位置信息，如果不存在则返回nil
    func getFriendLocation(for friendId: String) -> FriendLocationInfo? {
        return friendAnnotations.first { $0.friendLocation.userId == friendId }?.friendLocation
    }
    
    /// 获取在线好友的标注
    var onlineFriendAnnotations: [FriendMapAnnotation] {
        return friendAnnotations.filter { $0.friendLocation.isOnline }
    }
    
    /// 获取离线好友的标注
    var offlineFriendAnnotations: [FriendMapAnnotation] {
        return friendAnnotations.filter { !$0.friendLocation.isOnline }
    }
    
    /// 清除错误和成功消息
    func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    /// 清除所有好友标注
    func clearFriendAnnotations() {
        friendAnnotations = []
        lastUpdateTime = nil
    }
    
    // MARK: - Private Methods
    
    /// 设置观察者监听依赖组件的状态变化
    private func setupObservers() {
        // 监听好友视图模型的错误信息
        friendViewModel.$errorMessage
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.errorMessage = "好友列表错误: \(error)"
            }
            .store(in: &cancellables)
        
        // 监听好友位置管理器的错误信息
        friendsLocationManager.$errorMessage
            .compactMap { $0 }
            .sink { [weak self] error in
                self?.errorMessage = "位置获取错误: \(error)"
            }
            .store(in: &cancellables)
    }
}

// MARK: - 便利方法扩展

extension FriendMapViewModel {
    
    /// 获取好友数量统计信息
    var friendLocationStats: (total: Int, online: Int, offline: Int) {
        let total = friendAnnotations.count
        let online = onlineFriendAnnotations.count
        let offline = offlineFriendAnnotations.count
        return (total: total, online: online, offline: offline)
    }
    
    /// 是否有好友位置数据
    var hasFriendLocations: Bool {
        return !friendAnnotations.isEmpty
    }
    
    /// 格式化的最后更新时间
    var formattedLastUpdateTime: String? {
        guard let lastUpdateTime = lastUpdateTime else { return nil }
        
        let formatter = DateFormatter()
        formatter.dateStyle = .none
        formatter.timeStyle = .short
        return "最后更新: \(formatter.string(from: lastUpdateTime))"
    }
}
