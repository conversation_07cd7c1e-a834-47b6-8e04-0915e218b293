{"v": "5.9.6", "ip": 0, "op": 188, "fr": 29, "w": 1080, "h": 1080, "nm": "C", "assets": [], "layers": [{"ind": 1, "nm": "N", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [540, 540, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 2, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -27, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -0.563, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.375, "s": [0]}, {"t": 20, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-41.707, -14.65, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[3.06, 0.07], [-3.06, -0.07]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -27, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -17.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.375, "s": [100]}, {"t": 20, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -27, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -7.418, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.375, "s": [100]}, {"t": 20, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 3, "nm": "2", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5.375, "s": [0]}, {"t": 23, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-50.395, -14.27, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[4.835, 0.11], [-4.835, -0.11]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -14.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5.375, "s": [100]}, {"t": 23, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -24, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -4.418, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5.375, "s": [100]}, {"t": 23, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 4, "nm": "3", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 5.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [0]}, {"t": 26, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-39.38, -20.999, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[4.633, 3.973], [-4.633, -3.973]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -11.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [100]}, {"t": 26, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -21, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -1.418, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.375, "s": [100]}, {"t": 26, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 5, "nm": "4", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 8.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [0]}, {"t": 29, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-31.945, -22, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[0.575, 2.24], [-0.575, -2.24]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -8.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [100]}, {"t": 29, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -18, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 1.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.375, "s": [100]}, {"t": 29, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 6, "nm": "5", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 11.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [0]}, {"t": 32, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-34.625, -32.64, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[1.465, 5.72], [-1.465, -5.72]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [100]}, {"t": 32, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 4.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.375, "s": [100]}, {"t": 32, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 7, "nm": "6", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 14.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [0]}, {"t": 35, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [30.485, -22.75, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-0.715, 2.79], [0.715, -2.79]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -2.208, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [100]}, {"t": 35, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -12, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 7.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.375, "s": [100]}, {"t": 35, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 8, "nm": "7", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 17.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [0]}, {"t": 38, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [33.24, -33.485, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-1.25, 4.875], [1.25, -4.875]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [100]}, {"t": 38, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -9, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 10.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.375, "s": [100]}, {"t": 38, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 9, "nm": "8", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [0]}, {"t": 41, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [37.781, -20.999, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-4.633, 3.973], [4.633, -3.973]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 3.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [100]}, {"t": 41, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -6, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 13.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.375, "s": [100]}, {"t": 41, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 10, "nm": "9", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -0.063, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 23.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [0]}, {"t": 44, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [38.17, -14.02, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-3.53, 0.08], [3.53, -0.08]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 6.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [100]}, {"t": 44, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -3, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 16.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.375, "s": [100]}, {"t": 44, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 11, "nm": "1", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [3]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 2.938, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 26.438, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [0]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [49.585, -14.285, 0], "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-4.045, 0.095], [4.045, -0.095]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tm", "nm": "T", "s": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 9.793, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "e": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 19.583, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 29.375, "s": [100]}, {"t": 47, "s": [100]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "o": {"a": 0, "k": 0}, "m": 1}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}, {"ind": 12, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [-11.304, 41.815, 0], "l": 2}, "a": {"a": 0, "k": [-8.804, 34.315, 0], "l": 2}, "s": {"k": [{"i": {"x": [0.667, 0.667, 0.667], "y": [1, 1, 1]}, "o": {"x": [0.333, 0.333, 0.333], "y": [0, 0, 0]}, "t": 0, "s": [30, 30, 100]}, {"t": 31, "s": [110, 110, 100]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [1.934, 0], [-1.934, 0]], "o": [[1.93, 0], [-1.93, 0], [0, 0]], "v": [[-8.804, 35.815], [-8.804, 32.815], [-8.804, 35.815]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 13, "nm": "2", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -5, "s": [90]}, {"t": 42, "s": [-90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [-43, 12.58, 0], "l": 2}, "a": {"a": 0, "k": [-43.085, 12.399, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-38.5, 12.58], [-47.5, 12.58]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0]], "o": [[0, 0], [0, 0]], "v": [[-43, 8.08], [-43, 17.08]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 14, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -15, "s": [90]}, {"t": 32, "s": [-90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [28, 39, 0], "l": 2}, "a": {"a": 0, "k": [28, 39, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, -1.21], [1.93, 0], [0, 1.93], [-0.05, 0.24]], "o": [[0.94, 0.63], [0, 1.93], [-1.93, 0], [0, -0.26], [0, 0]], "v": [[30.44, 36.59], [32, 39.5], [28.5, 43], [25, 39.5], [25.08, 38.75]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.709, 0.062, 0.203, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.933], [1.933, 0], [0, 1.933], [-1.933, 0]], "o": [[0, 1.933], [-1.933, 0], [0, -1.933], [1.933, 0]], "v": [[32, 39.5], [28.5, 43], [25, 39.5], [28.5, 36]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 15, "nm": "1", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [-90]}, {"t": 47, "s": [90]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"a": 0, "k": [47.696, 2.156, 0], "l": 2}, "a": {"a": 0, "k": [47.696, 2.156, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.625, 1.484], [-2.801, -1.181], [1.395, -0.736]], "o": [[1.402, -0.494], [-1.181, 2.801], [-1.576, -0.664], [0, 0]], "v": [[46.61, 0.124], [49.833, -2.912], [52.764, 4.292], [48.094, 4.483]], "c": false}, "a": 0}}, {"ty": "st", "nm": "S", "bm": 0, "lc": 2, "lj": 2, "o": {"a": 0, "k": 100}, "w": {"a": 0, "k": 2}, "c": {"a": 0, "k": [0.176, 0.301, 0.631, 1]}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[-0.215, 0.509], [-0.509, -0.215], [-0.964, 2.287], [-0.509, -0.215], [0.215, -0.509], [-2.287, -0.964], [0.215, -0.509], [0.509, 0.215], [0.964, -2.287], [0.509, 0.215], [-0.215, 0.509], [2.287, 0.964]], "o": [[0.215, -0.509], [2.287, 0.964], [0.215, -0.509], [0.509, 0.215], [-0.964, 2.287], [0.509, 0.215], [-0.215, 0.509], [-2.287, -0.964], [-0.215, 0.509], [-0.509, -0.215], [0.964, -2.287], [-0.509, -0.215]], "v": [[41.707, -0.37], [43.017, -0.903], [48.912, -3.301], [50.221, -3.834], [50.754, -2.524], [53.153, 3.371], [53.686, 4.681], [52.376, 5.214], [46.481, 7.612], [45.171, 8.145], [44.638, 6.835], [42.24, 0.94]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0, 0.85, 0.976, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 1, "ty": 4}, {"ind": 16, "nm": "L", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [0]}, {"t": 21, "s": [-15]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [550, 655.288, 0], "ti": [0, 26.666, 0], "to": [0, -26.666, 0]}, {"t": 47, "s": [550, 495.289, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}, "a": {"a": 0, "k": [0, 9.411, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.859, 0], [0, 0], [0, 0.859], [0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0]], "o": [[0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0], [0.859, 0], [0, 0], [0, 0.859]], "v": [[13.445, -13], [6.555, -13], [5, -14.555], [5, -16.445], [6.555, -18], [13.445, -18], [15, -16.445], [15, -14.555]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 17, "nm": "F", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 0, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 12, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 15, "s": [100]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 20, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 39, "s": [0]}, {"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": 42, "s": [100]}, {"t": 47, "s": [0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();"}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [0]}, {"t": 21, "s": [-15]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [550, 655.288, 0], "ti": [0, 26.666, 0], "to": [0, -26.666, 0]}, {"t": 47, "s": [550, 495.289, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}, "a": {"a": 0, "k": [0, 9.411, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.859, 0], [0, 0], [0, 0.859], [0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0]], "o": [[0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0], [0.859, 0], [0, 0], [0, 0.859]], "v": [[13.445, -13], [6.555, -13], [5, -14.555], [5, -16.445], [6.555, -18], [13.445, -18], [15, -16.445], [15, -14.555]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 18, "nm": "F", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [0]}, {"t": 21, "s": [-15]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [550, 655.288, 0], "ti": [0, 26.666, 0], "to": [0, -26.666, 0]}, {"t": 47, "s": [550, 495.289, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}, "a": {"a": 0, "k": [0, 9.411, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0.859, 0], [0, 0], [0, 0.859], [0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0]], "o": [[0, 0], [-0.859, 0], [0, 0], [0, -0.859], [0, 0], [0.859, 0], [0, 0], [0, 0.859]], "v": [[13.445, -13], [6.555, -13], [5, -14.555], [5, -16.445], [6.555, -18], [13.445, -18], [15, -16.445], [15, -14.555]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.988, 0.96, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 19, "nm": "C", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 1, "k": [{"i": {"x": [0.667], "y": [1]}, "o": {"x": [0.333], "y": [0]}, "t": -26, "s": [0]}, {"t": 21, "s": [-15]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');"}, "p": {"k": [{"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 0, "s": [550, 655.288, 0], "ti": [0, 26.666, 0], "to": [0, -26.666, 0]}, {"t": 47, "s": [550, 495.289, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut('pingpong');", "a": 1, "l": 2}, "a": {"a": 0, "k": [0, 9.411, 0], "l": 2}, "s": {"a": 0, "k": [800, 800, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[1.767, 0], [0, 0], [0.559, -1.677], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.767, 0], [0, 0], [0, 0], [0, 0], [-0.559, -1.677]], "v": [[14.041, -22], [5.959, -22], [2.064, -19.193], [-1, -10], [21, -10], [17.936, -19.193]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [1.77, 0], [0, 0], [-0.56, -1.68]], "o": [[0, 0], [0, 0], [-0.56, -1.68], [0, 0], [1.77, 0], [0, 0]], "v": [[21, -10], [16, -10], [12.94, -19.19], [9.04, -22], [14.04, -22], [17.94, -19.19]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[1.767, 0], [0, 0], [0.559, -1.677], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-1.767, 0], [0, 0], [0, 0], [0, 0], [-0.559, -1.677]], "v": [[14.041, -22], [5.959, -22], [2.064, -19.193], [-1, -10], [21, -10], [17.936, -19.193]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.447, 0.588, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [1.731, -2.44], [0.019, -0.03], [-1.31, -1.8], [1.454, -2], [-0.813, -1.67], [0, 0], [0, 0], [0, 3.87]], "o": [[0, 0], [2.908, 0], [-0.019, 0.03], [-1.31, 1.8], [1.454, 2], [-1.215, 1.67], [0, 0], [0, 0], [-3.87, 0], [0, 0]], "v": [[-33, 3], [-25.501, 3], [-22.555, 8.91], [-22.612, 9], [-22.612, 15], [-22.612, 21], [-23.224, 26], [-23.22, 26], [-25.99, 26], [-33, 18.99]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[1.45, -2], [-0.05, -1.06], [-0.3, -0.61], [0, 0], [0.33, 0.06], [0.13, 0.03], [0.06, 0.02], [0.25, 0.1], [0.14, 0.06], [0.03, 0.02], [0.14, 0.07], [0.13, 0.09], [0.1, 0.07], [0.15, 0.12], [0.12, 0.12], [0, 0.01], [0.11, 0.13], [0.02, 0.03], [0.11, 0.15], [0.07, 0.11], [0.07, 0.12], [0.09, 0.2], [0.06, 0.17], [0, 0.85], [0, 0], [0, 0], [1.73, -2.44], [0.02, -0.03], [-1.31, -1.8]], "o": [[-0.78, 1.06], [0.02, 0.61], [0, 0], [-0.34, 0], [-0.13, -0.02], [-0.06, -0.01], [-0.27, -0.05], [-0.14, -0.04], [-0.04, -0.01], [-0.14, -0.06], [-0.15, -0.08], [-0.1, -0.06], [-0.16, -0.11], [-0.13, -0.1], [0, 0], [-0.13, -0.12], [-0.04, -0.03], [-0.13, -0.14], [-0.08, -0.1], [-0.07, -0.1], [-0.11, -0.19], [-0.07, -0.17], [-0.28, -0.75], [0, 0], [0, 0], [2.91, 0], [-0.02, 0.03], [-1.31, 1.8], [1.45, 2]], "v": [[-22.61, 21], [-23.7, 24.18], [-23.22, 26], [-25.99, 26], [-27, 25.92], [-27.39, 25.85], [-27.57, 25.81], [-28.35, 25.58], [-28.77, 25.42], [-28.86, 25.38], [-29.28, 25.18], [-29.71, 24.93], [-30.01, 24.73], [-30.47, 24.38], [-30.84, 24.05], [-30.85, 24.04], [-31.21, 23.66], [-31.3, 23.56], [-31.65, 23.12], [-31.87, 22.81], [-32.07, 22.48], [-32.37, 21.9], [-32.57, 21.4], [-33, 18.99], [-33, 3], [-25.5, 3], [-22.55, 8.91], [-22.61, 9], [-22.61, 15]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0, 0.85, 0.976, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.657], [1.657, 0], [0, 1.657], [-1.657, 0]], "o": [[0, 1.657], [-1.657, 0], [0, -1.657], [1.657, 0]], "v": [[11, 8], [8, 11], [5, 8], [8, 5]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.152, 0.235, 0.258, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -4.971], [4.971, 0], [0, 4.971], [-4.971, 0]], "o": [[0, 4.971], [-4.971, 0], [0, -4.971], [4.971, 0]], "v": [[17, 8], [8, 17], [-1, 8], [8, -1]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -1.45], [3.59, 0], [0.09, 0.01], [0, 0.68], [-3.35, 1.3]], "o": [[0, 3.59], [-0.09, 0], [-0.14, -0.64], [0, -3.81], [0.8, 1.08]], "v": [[6, 3.5], [-0.5, 10], [-0.78, 9.99], [-1, 8], [4.72, -0.37]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.49, 0.537, 0.549, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -0.68], [4.8, -0.19], [0, 0.87], [-4.69, 0], [-0.09, -0.01]], "o": [[0, 4.85], [-0.24, -0.79], [0, -4.69], [0.09, 0], [0.14, 0.64]], "v": [[17, 8], [8.37, 16.99], [8, 14.5], [16.5, 6], [16.78, 6.01]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.49, 0.537, 0.549, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -0.68], [4.8, -0.19], [0.12, 0], [0.91, 4.01], [0, 0.68], [-3.35, 1.3], [-1.16, 0], [-0.91, -4.01]], "o": [[0, 4.85], [-0.12, 0.01], [-4.29, 0], [-0.14, -0.64], [0, -3.81], [1.01, -0.41], [4.29, 0], [0.14, 0.64]], "v": [[17, 8], [8.37, 16.99], [8, 17], [-0.78, 9.99], [-1, 8], [4.72, -0.37], [8, -1], [16.78, 6.01]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.278, 0.36, 0.388, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-6.1, 0], [0, 7.18], [7.18, 0], [0.5, -6.72]], "o": [[1.41, 5.66], [7.18, 0], [0, -7.18], [-6.85, 0], [0, 0]], "v": [[-4.62, 11.14], [8, 21], [21, 8], [8, -5], [-4.96, 7.01]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -7.18], [7.18, 0], [0, 7.18], [-7.18, 0]], "o": [[0, 7.18], [-7.18, 0], [0, -7.18], [7.18, 0]], "v": [[21, 8], [8, 21], [-5, 8], [8, -5]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.988, 0.96, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [-0.29, 0], [0, 0], [0, -3.87], [0, 0], [0, 0], [0, 0], [-0.8, 1.14]], "o": [[0.28, -0.04], [0, 0], [3.87, 0], [0, 0], [0, 0], [0, 0], [0, -1.49], [0, 0]], "v": [[-26.84, -9.94], [-25.99, -10], [25.99, -10], [33, -2.99], [33, -1], [-33, -1], [-33, -2.99], [-31.74, -7]], "c": false}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [0, 0], [0, 0], [3.039, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -3.87], [0, 0], [3.039, 0]], "v": [[33, -2.99], [33, -1], [27.504, -1], [27.504, -2.99], [22, -10], [27.496, -10]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.878, 0.592, 0.247, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [0, 0], [0, 0], [-0.03, 0.24], [-0.04, 0.2], [-0.03, 0.11], [-0.03, 0.11], [-0.04, 0.09], [-0.06, 0.12], [-0.1, 0.17], [-0.06, 0.1], [-0.05, 0.08], [-0.1, 0.13], [-0.12, 0.14], [-0.05, 0.05], [-0.12, 0.11], [-0.54, 0.3], [-0.19, 0.08], [-0.2, 0.07], [-0.2, 0.05], [-0.2, 0.03], [-0.14, 0], [-0.21, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, -0.24], [0.02, -0.2], [0.02, -0.12], [0.03, -0.12], [0.03, -0.09], [0.04, -0.13], [0.07, -0.19], [0.04, -0.1], [0.04, -0.09], [0.09, -0.14], [0.1, -0.15], [0.04, -0.05], [0.1, -0.12], [0.44, -0.43], [0.18, -0.1], [0.19, -0.09], [0.19, -0.07], [0.2, -0.05], [0.13, -0.03], [0.21, -0.03], [0, 0], [3.87, 0]], "v": [[33, -2.99], [33, -1], [-33, -1], [-33, -2.99], [-32.96, -3.71], [-32.87, -4.31], [-32.8, -4.65], [-32.71, -4.99], [-32.62, -5.26], [-32.48, -5.63], [-32.23, -6.17], [-32.07, -6.47], [-31.93, -6.72], [-31.65, -7.12], [-31.31, -7.55], [-31.18, -7.69], [-30.85, -8.04], [-29.37, -9.13], [-28.82, -9.4], [-28.23, -9.63], [-27.63, -9.8], [-27.04, -9.91], [-26.63, -9.96], [-25.99, -10], [25.99, -10]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.8, 0.329, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -7.732], [7.732, 0], [0, 7.732], [-7.732, 0]], "o": [[0, 7.732], [-7.732, 0], [0, -7.732], [7.732, 0]], "v": [[24, 10], [10, 24], [-4, 10], [10, -4]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [3.87, 0], [0, 0], [0, 3.87], [0, 0], [-0.8, 1.14], [0, 0], [0, 0], [-0.29, 0], [0, 0]], "o": [[0, 0], [0, 3.87], [0, 0], [-3.87, 0], [0, 0], [0, -1.49], [0, 0], [0, 0], [0.28, -0.04], [0, 0], [3.87, 0]], "v": [[33, -2.99], [33, 18.99], [25.99, 26], [-25.99, 26], [-33, 18.99], [-33, -2.99], [-31.74, -7], [-26, -7], [-26.84, -9.94], [-25.99, -10], [25.99, -10]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, -3.87], [0, 0], [3.039, 0], [0, 0], [0, 3.87], [0, 0], [3.039, 0], [0, 0]], "o": [[0, 0], [0, 3.87], [0, 0], [3.039, 0], [0, 0], [0, -3.87], [0, 0], [3.039, 0]], "v": [[33, -2.99], [33, 18.99], [27.496, 26], [22, 26], [27.504, 18.99], [27.504, -2.99], [22, -10], [27.496, -10]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.847, 0.266, 0.364, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 50}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[3.871, 0], [0, 0], [0, 3.871], [0, 0], [-3.871, 0], [0, 0], [0, -3.871], [0, 0]], "o": [[0, 0], [-3.871, 0], [0, 0], [0, -3.871], [0, 0], [3.871, 0], [0, 0], [0, 3.871]], "v": [[25.99, 26], [-25.99, 26], [-33, 18.99], [-33, -2.99], [-25.99, -10], [25.99, -10], [33, -2.99], [33, 18.99]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [1, 0.447, 0.588, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "ty": 4}, {"ind": 20, "nm": "S", "sr": 1, "ks": {"o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.333, "y": 0.333}, "t": 0, "s": [-20, -12, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 10, "s": [-20, -12, 0], "ti": [0, 0, 0], "to": [0, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 15, "s": [-20, -10, 0], "ti": [0, 0.333, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 20, "s": [-20, -12, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 0.667}, "o": {"x": 0.333, "y": 0.333}, "t": 31, "s": [-20, -12, 0], "ti": [0, 0, 0], "to": [0, 0, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 37, "s": [-20, -12, 0], "ti": [0, 0, 0], "to": [0, 0.333, 0]}, {"i": {"x": 0.667, "y": 1}, "o": {"x": 0.333, "y": 0}, "t": 42, "s": [-20, -10, 0], "ti": [0, 0.333, 0], "to": [0, 0, 0]}, {"t": 47, "s": [-20, -12, 0]}], "x": "var $bm_rt;\n$bm_rt = loopOut();", "a": 1, "l": 2}, "a": {"a": 0, "k": [-20, -12, 0], "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "l": 2}}, "shapes": [{"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-1.322, 0], [0, 0], [0, -1.322]], "o": [[0, 0], [0, 0], [0, -1.322], [0, 0], [1.322, 0], [0, 0]], "v": [[-17, -10], [-23, -10], [-23, -12.606], [-20.606, -15], [-19.394, -15], [-17, -12.606]], "c": true}, "a": 0}}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}, {"ty": "gr", "nm": "G", "bm": 0, "it": [{"ty": "sh", "nm": "P", "ind": 0, "ks": {"k": {"i": [[0, 0], [0, 0], [0, 0], [-1.322, 0], [0, 0], [0, -1.322]], "o": [[0, 0], [0, 0], [0, -1.322], [0, 0], [1.322, 0], [0, 0]], "v": [[-17, -10], [-23, -10], [-23, -12.606], [-20.606, -15], [-19.394, -15], [-17, -12.606]], "c": true}, "a": 0}}, {"ty": "fl", "nm": "F", "bm": 0, "c": {"a": 0, "k": [0.65, 0.976, 1, 1]}, "o": {"a": 0, "k": 100}, "r": 1}, {"ty": "tr", "o": {"a": 0, "k": 100}, "r": {"a": 0, "k": 0}, "p": {"a": 0, "k": [0, 0]}, "a": {"a": 0, "k": [0, 0]}, "s": {"a": 0, "k": [100, 100]}, "nm": "T", "sk": {"a": 0, "k": 0}, "sa": {"a": 0, "k": 0}}]}], "ip": 0, "op": 600, "st": 0, "parent": 19, "ty": 4}], "markers": []}