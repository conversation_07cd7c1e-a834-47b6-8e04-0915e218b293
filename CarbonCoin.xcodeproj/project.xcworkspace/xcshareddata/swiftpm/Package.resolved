{"originHash": "5145cd4058f5427eb36f124f49e859227752a6068ec57519392eb2757de67d81", "pins": [{"identity": "lottie-spm", "kind": "remoteSourceControl", "location": "https://github.com/airbnb/lottie-spm.git", "state": {"revision": "04f2fd18cc9404a0a0917265a449002674f24ec9", "version": "4.5.2"}}, {"identity": "popupview", "kind": "remoteSourceControl", "location": "https://github.com/exyte/PopupView.git", "state": {"revision": "6d875d432e80a65810375be102fb04ae748e9beb", "version": "4.1.11"}}, {"identity": "swiftui-introspect", "kind": "remoteSourceControl", "location": "https://github.com/siteline/swiftui-introspect", "state": {"revision": "807f73ce09a9b9723f12385e592b4e0aaebd3336", "version": "1.3.0"}}, {"identity": "tocropviewcontroller", "kind": "remoteSourceControl", "location": "https://github.com/TimOliver/TOCropViewController.git", "state": {"revision": "a634cb7cdfd580006e79a6e74e64417fe9e9783b", "version": "2.7.4"}}], "version": 3}